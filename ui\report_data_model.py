"""
报告数据模型
定义报告生成所需的数据结构和模板
"""

from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional, Any
import json


@dataclass
class FileInfo:
    """文件信息"""
    file_id: int
    file_name: str
    file_path: str
    test_time: datetime
    vehicle_type: str
    component: str
    sensor_type: str
    sensor_number: str
    sampling_rate: Optional[float] = None
    channel_info: Optional[Dict] = None


@dataclass
class FeatureResult:
    """特征提取结果"""
    feature_type: str
    feature_name: str
    feature_value: float
    extraction_time: datetime
    parameters: Optional[Dict] = None


@dataclass
class DiagnosisResult:
    """诊断结果"""
    algorithm_type: str  # 'classical' or 'deep_learning'
    algorithm_name: str
    fault_type: Optional[str]
    confidence: float
    status: str  # 'normal', 'warning', 'fault'
    diagnosis_time: datetime
    details: Optional[Dict] = None


@dataclass
class AnalysisSummary:
    """分析摘要"""
    total_diagnoses: int
    normal_count: int
    warning_count: int
    fault_count: int
    last_diagnosis: Optional[datetime]
    avg_confidence: float
    total_features: int
    feature_types: int
    last_extraction: Optional[datetime]


@dataclass
class ReportData:
    """报告数据"""
    file_info: FileInfo
    feature_results: List[FeatureResult]
    diagnosis_results: List[DiagnosisResult]
    analysis_summary: Optional[AnalysisSummary] = None


class ReportDataValidator:
    """报告数据验证器"""
    
    @staticmethod
    def validate_report_data(report_data: ReportData) -> Dict[str, Any]:
        """验证报告数据"""
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 验证文件信息
        if not report_data.file_info:
            validation_result['is_valid'] = False
            validation_result['errors'].append("缺少文件信息")
        
        # 验证诊断结果
        if not report_data.diagnosis_results:
            validation_result['warnings'].append("无诊断结果数据")
        
        # 验证特征结果
        if not report_data.feature_results:
            validation_result['warnings'].append("无特征提取数据")
        
        return validation_result


class SingleFileReportTemplate:
    """单文件报告模板"""
    
    def generate_content(self, report_data: ReportData) -> Dict[str, Any]:
        """生成单文件报告内容"""
        current_time = datetime.now()
        
        # 根据参考图片格式生成报告内容 - 使用"变速箱检测报告"格式
        component_name = report_data.file_info.component
        if '变速箱' in component_name or 'transmission' in component_name.lower():
            title = '变速箱检测报告'
        elif '轴承' in component_name or 'bearing' in component_name.lower():
            title = '轴承故障检测报告'
        else:
            title = f'{component_name}检测报告'

        report_content = {
            'title': title,
            'subtitle': f'基于 {report_data.file_info.file_name} 的检测分析',
            'generated_time': current_time.strftime('%Y年%m月%d日 %H:%M:%S'),
            'sections': []
        }
        
        # 基本信息章节 - 按照参考图片格式
        basic_info = {
            'title': '检测基本信息',
            'type': 'info',
            'content': {
                '检测日期': report_data.file_info.test_time.strftime('%Y年%m月%d日 %H:%M:%S'),
                '采样参数': f"{report_data.file_info.sampling_rate or 48000}Hz",
                '通道参数': f"物理量: {self._get_physical_quantity(report_data.file_info.sensor_type)}, 灵敏度: 3 mV/V",
                '车辆类型': report_data.file_info.vehicle_type,
                '检测目标': report_data.file_info.component,
                '测量编号': report_data.file_info.file_name,
                '检测人员': '系统自动检测',
                '检测项目': '轴承故障诊断检测',
                '传感器类型': report_data.file_info.sensor_type,
                '传感器编号': report_data.file_info.sensor_number
            }
        }
        report_content['sections'].append(basic_info)
        
        # 诊断结果章节 - 已删除
        # 由于显示问题，暂时移除此章节
        
        # 特征分析章节
        if report_data.feature_results:
            features_section = {
                'title': '特征分析结果',
                'type': 'features',
                'content': self._format_feature_results(report_data.feature_results)
            }
            report_content['sections'].append(features_section)
        
        # 分析摘要章节
        if report_data.analysis_summary:
            summary_section = {
                'title': '分析摘要',
                'type': 'summary',
                'content': self._format_analysis_summary(report_data.analysis_summary)
            }
            report_content['sections'].append(summary_section)
        
        return report_content
    
    def _get_physical_quantity(self, sensor_type: str) -> str:
        """根据传感器类型获取物理量"""
        if '振动' in sensor_type or 'vibration' in sensor_type.lower():
            return '振动'
        elif '声音' in sensor_type or 'sound' in sensor_type.lower() or 'audio' in sensor_type.lower():
            return '声音'
        elif '温度' in sensor_type or 'temperature' in sensor_type.lower():
            return '温度'
        else:
            return '振动'  # 默认
    
    def _format_diagnosis_results(self, diagnosis_results: List[DiagnosisResult]) -> List[Dict]:
        """格式化诊断结果"""
        formatted_results = []
        for result in diagnosis_results:
            formatted_results.append({
                '算法类型': '经典分类器' if result.algorithm_type == 'classical' else '深度学习',
                '算法名称': result.algorithm_name,
                '故障类型': result.fault_type or '正常',
                '置信度': f"{result.confidence:.2%}",
                '诊断状态': self._get_status_text(result.status),
                '诊断时间': result.diagnosis_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        return formatted_results
    
    def _format_feature_results(self, feature_results: List[FeatureResult]) -> List[Dict]:
        """格式化特征结果"""
        formatted_results = []
        for result in feature_results:
            formatted_results.append({
                '特征类型': result.feature_type,
                '特征名称': result.feature_name,
                '特征值': f"{result.feature_value:.6f}",
                '提取时间': result.extraction_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        return formatted_results
    
    def _format_analysis_summary(self, summary: AnalysisSummary) -> Dict[str, str]:
        """格式化分析摘要"""
        return {
            '总诊断次数': str(summary.total_diagnoses),
            '正常次数': str(summary.normal_count),
            '警告次数': str(summary.warning_count),
            '故障次数': str(summary.fault_count),
            '平均置信度': f"{summary.avg_confidence:.2%}",
            '特征总数': str(summary.total_features),
            '特征类型数': str(summary.feature_types),
            '最后诊断时间': summary.last_diagnosis.strftime('%Y-%m-%d %H:%M:%S') if summary.last_diagnosis else '无',
            '最后特征提取时间': summary.last_extraction.strftime('%Y-%m-%d %H:%M:%S') if summary.last_extraction else '无'
        }
    
    def _get_status_text(self, status: str) -> str:
        """获取状态文本"""
        status_map = {
            'normal': '正常',
            'warning': '警告',
            'fault': '故障'
        }
        return status_map.get(status, status)


class ComparativeReportTemplate:
    """对比报告模板"""
    
    def generate_content(self, report_data_list: List[ReportData]) -> Dict[str, Any]:
        """生成对比报告内容"""
        current_time = datetime.now()
        
        report_content = {
            'title': '轴承故障检测对比报告',
            'subtitle': f'基于 {len(report_data_list)} 个文件的对比分析',
            'generated_time': current_time.strftime('%Y年%m月%d日 %H:%M:%S'),
            'sections': []
        }
        
        # 文件列表章节
        file_list_section = {
            'title': '分析文件列表',
            'type': 'file_list',
            'content': self._format_file_list(report_data_list)
        }
        report_content['sections'].append(file_list_section)
        
        # 对比分析章节
        comparison_section = {
            'title': '对比分析结果',
            'type': 'comparison',
            'content': self._format_comparison_results(report_data_list)
        }
        report_content['sections'].append(comparison_section)
        
        return report_content
    
    def _format_file_list(self, report_data_list: List[ReportData]) -> List[Dict]:
        """格式化文件列表"""
        file_list = []
        for i, report_data in enumerate(report_data_list, 1):
            file_list.append({
                '序号': str(i),
                '文件名': report_data.file_info.file_name,
                '车型': report_data.file_info.vehicle_type,
                '部件': report_data.file_info.component,
                '测试时间': report_data.file_info.test_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        return file_list
    
    def _format_comparison_results(self, report_data_list: List[ReportData]) -> Dict[str, str]:
        """格式化对比结果"""
        total_files = len(report_data_list)
        fault_files = sum(1 for data in report_data_list 
                         if any(result.status == 'fault' for result in data.diagnosis_results))
        
        return {
            '分析文件总数': str(total_files),
            '检测到故障的文件数': str(fault_files),
            '故障检出率': f"{fault_files/total_files:.2%}" if total_files > 0 else "0%",
            '分析完成时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }


class ReferenceFormatReportTemplate:
    """参考格式报告模板 - 完全按照用户提供的参考图片格式，包含完整的变速箱检测报告内容"""

    def generate_content(self, report_data: ReportData) -> Dict[str, Any]:
        """生成完全按照参考格式的报告内容，包含完整的变速箱检测信息"""
        current_time = datetime.now()

        # 按照参考图片的确切格式
        report_content = {
            'title': '变速箱检测报告',
            'subtitle': '',  # 参考格式中没有副标题
            'generated_time': current_time.strftime('%Y年%m月%d日 %H:%M:%S'),
            'sections': []
        }

        # 1. 基本信息章节 - 完全按照参考格式
        main_info = {
            'title': '基本检测信息',
            'type': 'reference_format',
            'content': {
                '检测日期': report_data.file_info.test_time.strftime('%Y年%m月%d日  %H:%M:%S'),
                '采样参数': f"{report_data.file_info.sampling_rate or 48000}Hz",
                '通道参数': f"物理量：声音        灵敏度：3 mV/V",
                '车辆类型': report_data.file_info.vehicle_type or "XXX",
                '检测目标': '变速箱',
                '测量编号': report_data.file_info.file_name or "XXXXXXXXX",
                '检测人员': "XXX",
                '检测项目': "单次实验检测"
            }
        }
        report_content['sections'].append(main_info)

        # 2. 变速箱故障诊断结果章节 - 已删除
        # 由于显示问题，暂时移除此章节

        # 3. 变速箱特征分析结果章节
        if report_data.feature_results:
            features_section = {
                'title': '变速箱特征分析结果',
                'type': 'features',
                'content': self._format_feature_results(report_data.feature_results)
            }
            report_content['sections'].append(features_section)

        # 4. 变速箱检测分析摘要章节
        summary_section = {
            'title': '变速箱检测分析摘要',
            'type': 'summary',
            'content': self._format_analysis_summary(report_data.analysis_summary, report_data.diagnosis_results)
        }
        report_content['sections'].append(summary_section)

        # 5. 变速箱检测结论章节
        conclusion_section = {
            'title': '变速箱检测结论',
            'type': 'info',
            'content': self._format_conclusion(report_data.diagnosis_results, report_data.analysis_summary)
        }
        report_content['sections'].append(conclusion_section)

        return report_content

    def _format_diagnosis_results(self, diagnosis_results: List[DiagnosisResult]) -> List[Dict[str, str]]:
        """格式化变速箱诊断结果"""
        formatted_results = []
        for result in diagnosis_results:
            formatted_results.append({
                '算法类型': '经典机器学习' if result.algorithm_type == 'classical' else '深度学习',
                '算法名称': result.algorithm_name,
                '诊断结果': result.fault_type,
                '置信度': f"{result.confidence:.2%}",
                '状态评估': self._get_status_description(result.status),
                '诊断时间': result.diagnosis_time.strftime('%Y-%m-%d %H:%M:%S')
            })
        return formatted_results

    def _format_feature_results(self, feature_results: List[FeatureResult]) -> Dict[str, str]:
        """格式化变速箱特征分析结果"""
        feature_summary = {}

        # 按特征类型分组
        time_domain_features = []
        freq_domain_features = []

        for feature in feature_results:
            if '时域' in feature.feature_type:
                time_domain_features.append(f"{feature.feature_name}: {feature.feature_value:.3f}")
            elif '频域' in feature.feature_type:
                freq_domain_features.append(f"{feature.feature_name}: {feature.feature_value:.3f}")
            else:
                time_domain_features.append(f"{feature.feature_name}: {feature.feature_value:.3f}")

        feature_summary['时域特征'] = '; '.join(time_domain_features) if time_domain_features else '无'
        feature_summary['频域特征'] = '; '.join(freq_domain_features) if freq_domain_features else '无'
        feature_summary['特征提取时间'] = feature_results[-1].extraction_time.strftime('%Y-%m-%d %H:%M:%S') if feature_results else '无'
        feature_summary['总特征数量'] = str(len(feature_results))

        return feature_summary

    def _format_analysis_summary(self, summary: AnalysisSummary, diagnosis_results: List[DiagnosisResult]) -> Dict[str, str]:
        """格式化变速箱分析摘要"""
        # 计算故障类型分布
        fault_types = {}
        for result in diagnosis_results:
            fault_type = result.fault_type
            fault_types[fault_type] = fault_types.get(fault_type, 0) + 1

        # 主要故障类型
        main_fault_type = max(fault_types.items(), key=lambda x: x[1])[0] if fault_types else '正常'

        return {
            '总诊断次数': str(summary.total_diagnoses),
            '正常状态次数': str(summary.normal_count),
            '警告状态次数': str(summary.warning_count),
            '故障状态次数': str(summary.fault_count),
            '平均置信度': f"{summary.avg_confidence:.2%}",
            '主要诊断结果': main_fault_type,
            '最后诊断时间': summary.last_diagnosis.strftime('%Y-%m-%d %H:%M:%S') if summary.last_diagnosis else '无',
            '特征分析完成度': f"{summary.total_features}个特征已分析"
        }

    def _format_conclusion(self, diagnosis_results: List[DiagnosisResult], summary: AnalysisSummary) -> Dict[str, str]:
        """格式化变速箱检测结论"""
        # 综合评估
        if summary.fault_count > 0:
            overall_status = "检测到故障"
            recommendation = "建议立即进行详细检查和维修"
        elif summary.warning_count > 0:
            overall_status = "存在异常征象"
            recommendation = "建议加强监测，定期复查"
        else:
            overall_status = "运行状态正常"
            recommendation = "继续正常使用，定期检测"

        # 最高置信度结果
        if diagnosis_results:
            best_result = max(diagnosis_results, key=lambda x: x.confidence)
            best_algorithm = best_result.algorithm_name
            best_confidence = f"{best_result.confidence:.2%}"
        else:
            best_algorithm = "无"
            best_confidence = "无"

        return {
            '变速箱整体状态': overall_status,
            '检测可靠性': best_confidence,
            '最佳诊断算法': best_algorithm,
            '维护建议': recommendation,
            '下次检测建议': "建议3个月后复查",
            '报告生成时间': datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'),
            '技术负责人': "XXX",
            '审核状态': "已审核"
        }

    def _get_status_description(self, status: str) -> str:
        """获取状态描述"""
        status_map = {
            'normal': '正常',
            'warning': '警告',
            'fault': '故障'
        }
        return status_map.get(status, '未知')
