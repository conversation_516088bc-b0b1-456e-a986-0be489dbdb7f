"""
调试视觉渲染问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_table_visibility():
    """调试表格可见性"""
    print("🔍 调试表格可见性...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from PyQt5.QtCore import Qt
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        # 创建测试诊断数据
        diagnosis_data = [
            {
                '算法类型': '经典机器学习',
                '算法名称': 'SVM分类器',
                '诊断结果': '正常',
                '置信度': '85.00%',
                '状态评估': '正常',
                '诊断时间': '2025-08-05 16:57:44'
            },
            {
                '算法类型': '深度学习',
                '算法名称': 'CNN模型',
                '诊断结果': '正常',
                '置信度': '92.00%',
                '状态评估': '正常',
                '诊断时间': '2025-08-05 16:57:44'
            }
        ]
        
        # 创建诊断表格
        table = generator.create_diagnosis_content(diagnosis_data)
        
        print(f"✓ 表格创建成功")
        print(f"  表格类型: {type(table)}")
        print(f"  表格可见性: {table.isVisible()}")
        print(f"  表格启用状态: {table.isEnabled()}")
        print(f"  表格大小: {table.size()}")
        print(f"  表格最小大小: {table.minimumSize()}")
        print(f"  表格最大大小: {table.maximumSize()}")
        print(f"  表格几何: {table.geometry()}")
        
        # 检查表格样式
        style_sheet = table.styleSheet()
        print(f"  表格样式表长度: {len(style_sheet)}")
        if style_sheet:
            print(f"  样式表前100字符: {style_sheet[:100]}...")
        
        # 检查表格内容
        print(f"  表格行数: {table.rowCount()}")
        print(f"  表格列数: {table.columnCount()}")
        
        # 检查每个单元格
        print(f"  单元格内容检查:")
        for row in range(table.rowCount()):
            for col in range(table.columnCount()):
                item = table.item(row, col)
                if item:
                    text = item.text()
                    print(f"    [{row},{col}]: '{text}' (长度: {len(text)})")
                else:
                    print(f"    [{row},{col}]: None")
        
        # 检查表头
        print(f"  表头检查:")
        for col in range(table.columnCount()):
            header_item = table.horizontalHeaderItem(col)
            if header_item:
                print(f"    列 {col}: '{header_item.text()}'")
            else:
                print(f"    列 {col}: None")
        
        # 检查列宽
        print(f"  列宽检查:")
        for col in range(table.columnCount()):
            width = table.columnWidth(col)
            print(f"    列 {col}: {width}px")
        
        # 检查行高
        print(f"  行高检查:")
        for row in range(table.rowCount()):
            height = table.rowHeight(row)
            print(f"    行 {row}: {height}px")
        
        # 创建测试窗口来显示表格
        print(f"\n🖥️ 创建测试窗口...")
        
        test_window = QMainWindow()
        test_window.setWindowTitle("诊断表格测试")
        test_window.setGeometry(100, 100, 800, 400)
        
        central_widget = QWidget()
        test_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.addWidget(table)
        
        # 显示窗口
        test_window.show()
        print(f"✓ 测试窗口显示成功")
        print(f"  窗口大小: {test_window.size()}")
        print(f"  中央组件大小: {central_widget.size()}")
        print(f"  表格在窗口中的大小: {table.size()}")
        
        # 强制更新
        table.update()
        table.repaint()
        
        print(f"✓ 表格更新完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 表格可见性调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_section_widget():
    """调试章节组件"""
    print("\n📋 调试章节组件...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        # 创建测试章节
        test_section = {
            'title': '变速箱故障诊断结果',
            'type': 'diagnosis',
            'content': [
                {
                    '算法类型': '经典机器学习',
                    '算法名称': 'SVM分类器',
                    '诊断结果': '正常',
                    '置信度': '85.00%',
                    '状态评估': '正常',
                    '诊断时间': '2025-08-05 16:57:44'
                },
                {
                    '算法类型': '深度学习',
                    '算法名称': 'CNN模型',
                    '诊断结果': '正常',
                    '置信度': '92.00%',
                    '状态评估': '正常',
                    '诊断时间': '2025-08-05 16:57:44'
                }
            ]
        }
        
        # 创建章节组件
        section_widget = generator.create_section_widget(test_section)
        
        print(f"✓ 章节组件创建成功")
        print(f"  组件类型: {type(section_widget)}")
        print(f"  组件可见性: {section_widget.isVisible()}")
        print(f"  组件大小: {section_widget.size()}")
        print(f"  组件最小大小: {section_widget.minimumSize()}")
        print(f"  组件最大大小: {section_widget.maximumSize()}")
        
        # 检查章节组件的子组件
        print(f"  子组件检查:")
        children = section_widget.findChildren(object)
        for i, child in enumerate(children[:10]):  # 只检查前10个
            print(f"    {i+1}. {type(child)} - 可见: {child.isVisible() if hasattr(child, 'isVisible') else 'N/A'}")
        
        # 检查布局
        layout = section_widget.layout()
        if layout:
            print(f"  布局类型: {type(layout)}")
            print(f"  布局项目数: {layout.count()}")
            for i in range(layout.count()):
                item = layout.itemAt(i)
                if item:
                    widget = item.widget()
                    if widget:
                        print(f"    项目 {i}: {type(widget)} - 大小: {widget.size()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 章节组件调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_preview_area():
    """调试预览区域"""
    print("\n🖥️ 调试预览区域...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        # 检查预览区域
        preview_area = generator.preview_area
        print(f"✓ 预览区域存在: {type(preview_area)}")
        print(f"  预览区域大小: {preview_area.size()}")
        print(f"  预览区域可见性: {preview_area.isVisible()}")
        print(f"  预览区域启用状态: {preview_area.isEnabled()}")
        
        # 检查预览区域的widget
        preview_widget = preview_area.widget()
        if preview_widget:
            print(f"✓ 预览组件存在: {type(preview_widget)}")
            print(f"  预览组件大小: {preview_widget.size()}")
            print(f"  预览组件可见性: {preview_widget.isVisible()}")
            
            # 检查预览组件的子组件
            children = preview_widget.findChildren(object)
            print(f"  预览组件子组件数: {len(children)}")
            
            # 查找表格组件
            from PyQt5.QtWidgets import QTableWidget
            tables = preview_widget.findChildren(QTableWidget)
            print(f"  找到表格数量: {len(tables)}")
            
            for i, table in enumerate(tables):
                print(f"    表格 {i+1}:")
                print(f"      大小: {table.size()}")
                print(f"      可见性: {table.isVisible()}")
                print(f"      行数: {table.rowCount()}")
                print(f"      列数: {table.columnCount()}")
                print(f"      几何: {table.geometry()}")
        else:
            print("✗ 预览组件不存在")
        
        return True
        
    except Exception as e:
        print(f"✗ 预览区域调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主调试函数"""
    print("🚀 开始调试视觉渲染问题\n")
    
    # 1. 调试表格可见性
    table_test = debug_table_visibility()
    
    # 2. 调试章节组件
    section_test = debug_section_widget()
    
    # 3. 调试预览区域
    preview_test = debug_preview_area()
    
    print(f"\n📊 调试结果总结:")
    print(f"   表格可见性: {'✓ 通过' if table_test else '✗ 失败'}")
    print(f"   章节组件: {'✓ 通过' if section_test else '✗ 失败'}")
    print(f"   预览区域: {'✓ 通过' if preview_test else '✗ 失败'}")
    
    print("\n💡 如果所有测试都通过，问题可能在于:")
    print("   1. 表格内容被其他元素遮挡")
    print("   2. 滚动区域设置问题")
    print("   3. 样式表冲突")
    print("   4. 布局管理器问题")


if __name__ == "__main__":
    main()
