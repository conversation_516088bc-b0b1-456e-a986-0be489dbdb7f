"""
报告导出器
负责将报告导出为不同格式（PDF、HTML等）
"""

import os
import base64
from datetime import datetime
from typing import Dict, Any, Optional
from ui.styles import ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY, PRIMARY_BG


class ReportExporter:
    """报告导出器"""
    
    def __init__(self):
        self.temp_dir = "temp"
        if not os.path.exists(self.temp_dir):
            os.makedirs(self.temp_dir)
    
    def export_to_html(self, report_content: Dict[str, Any], file_path: str, charts: Optional[Dict] = None):
        """导出为HTML格式"""
        html_content = self._generate_html_content(report_content, charts)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def export_to_pdf(self, report_content: Dict[str, Any], file_path: str, charts: Optional[Dict] = None):
        """导出为PDF格式"""
        try:
            from reportlab.lib.pagesizes import A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            import io
            
            # 注册中文字体
            try:
                pdfmetrics.registerFont(TTFont('SimHei', 'C:/Windows/Fonts/simhei.ttf'))
                font_name = 'SimHei'
            except:
                font_name = 'Helvetica'  # 备用字体
            
            # 创建PDF文档
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            story = []
            
            # 获取样式
            styles = getSampleStyleSheet()
            
            # 标题样式
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontName=font_name,
                fontSize=18,
                spaceAfter=30,
                alignment=1  # 居中
            )
            
            # 章节标题样式
            section_style = ParagraphStyle(
                'SectionTitle',
                parent=styles['Heading2'],
                fontName=font_name,
                fontSize=14,
                spaceAfter=12
            )
            
            # 正文样式
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontName=font_name,
                fontSize=10,
                spaceAfter=6
            )
            
            # 添加标题
            title = Paragraph(report_content.get('title', '检测报告'), title_style)
            story.append(title)
            
            # 添加副标题
            if report_content.get('subtitle'):
                subtitle = Paragraph(report_content.get('subtitle'), normal_style)
                story.append(subtitle)
            
            # 添加生成时间
            if report_content.get('generated_time'):
                time_text = f"生成时间: {report_content.get('generated_time')}"
                time_para = Paragraph(time_text, normal_style)
                story.append(time_para)
            
            story.append(Spacer(1, 20))
            
            # 添加各个章节
            for section in report_content.get('sections', []):
                # 章节标题
                section_title = Paragraph(section.get('title', ''), section_style)
                story.append(section_title)
                
                # 章节内容
                section_type = section.get('type', '')
                content = section.get('content', {})
                
                if section_type in ['info', 'summary', 'comparison']:
                    table_data = self._prepare_table_data(content)
                    if table_data:
                        table = Table(table_data, colWidths=[2*inch, 3*inch])
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 9),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(table)
                
                elif section_type == 'diagnosis':
                    if isinstance(content, list) and content:
                        # 诊断结果表格
                        headers = ['算法类型', '算法名称', '故障类型', '置信度', '诊断状态']
                        table_data = [headers]
                        
                        for item in content:
                            row = [
                                item.get('算法类型', ''),
                                item.get('算法名称', ''),
                                item.get('故障类型', ''),
                                item.get('置信度', ''),
                                item.get('诊断状态', '')
                            ]
                            table_data.append(row)
                        
                        table = Table(table_data)
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, -1), font_name),
                            ('FONTSIZE', (0, 0), (-1, -1), 8),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black)
                        ]))
                        story.append(table)
                
                story.append(Spacer(1, 12))
            
            # 添加图表（如果有）
            if charts:
                story.append(Paragraph("图表分析", section_style))
                for chart_name, chart_data in charts.items():
                    if chart_data and chart_data.startswith('data:image/png;base64,'):
                        # 解码base64图片
                        image_data = base64.b64decode(chart_data.split(',')[1])
                        image_buffer = io.BytesIO(image_data)
                        
                        # 添加图片到PDF
                        img = Image(image_buffer, width=5*inch, height=3*inch)
                        story.append(img)
                        story.append(Spacer(1, 12))
            
            # 生成PDF
            doc.build(story)
            
        except ImportError:
            raise ImportError("PDF导出需要安装reportlab库。请运行: pip install reportlab")
        except Exception as e:
            raise Exception(f"PDF导出失败: {str(e)}")
    
    def _generate_html_content(self, report_content: Dict[str, Any], charts: Optional[Dict] = None) -> str:
        """生成HTML内容"""
        html = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{report_content.get('title', '检测报告')}</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                    line-height: 1.6;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 30px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid {ACCENT_COLOR};
                    padding-bottom: 20px;
                }}
                .title {{
                    font-size: 28px;
                    font-weight: bold;
                    color: {ACCENT_COLOR};
                    margin-bottom: 10px;
                }}
                .subtitle {{
                    font-size: 16px;
                    color: {TEXT_SECONDARY};
                    margin-bottom: 10px;
                }}
                .generated-time {{
                    font-size: 14px;
                    color: {TEXT_SECONDARY};
                }}
                .section {{
                    margin-bottom: 30px;
                }}
                .section-title {{
                    font-size: 20px;
                    font-weight: bold;
                    color: {ACCENT_COLOR};
                    margin-bottom: 15px;
                    border-left: 4px solid {ACCENT_COLOR};
                    padding-left: 10px;
                }}
                .info-table, .diagnosis-table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                .info-table th, .info-table td,
                .diagnosis-table th, .diagnosis-table td {{
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: left;
                }}
                .info-table th, .diagnosis-table th {{
                    background-color: {ACCENT_COLOR};
                    color: white;
                    font-weight: bold;
                }}
                .info-table tr:nth-child(even),
                .diagnosis-table tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
                .chart-container {{
                    text-align: center;
                    margin: 20px 0;
                }}
                .chart-container img {{
                    max-width: 100%;
                    height: auto;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }}
                @media print {{
                    body {{ background-color: white; }}
                    .container {{ box-shadow: none; }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <div class="title">{report_content.get('title', '检测报告')}</div>
                    <div class="subtitle">{report_content.get('subtitle', '')}</div>
                    <div class="generated-time">生成时间: {report_content.get('generated_time', '')}</div>
                </div>
        """
        
        # 添加各个章节
        for section in report_content.get('sections', []):
            html += self._generate_html_section(section)
        
        # 添加图表
        if charts:
            html += '<div class="section"><div class="section-title">图表分析</div>'
            for chart_name, chart_data in charts.items():
                if chart_data:
                    html += f'''
                    <div class="chart-container">
                        <h4>{self._get_chart_title(chart_name)}</h4>
                        <img src="{chart_data}" alt="{chart_name}">
                    </div>
                    '''
            html += '</div>'
        
        html += """
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _generate_html_section(self, section: Dict) -> str:
        """生成HTML章节"""
        section_html = f'''
        <div class="section">
            <div class="section-title">{section.get('title', '')}</div>
        '''
        
        section_type = section.get('type', '')
        content = section.get('content', {})
        
        if section_type in ['info', 'summary', 'comparison', 'reference_format']:
            section_html += self._generate_html_table(content, ['项目', '值'], 'info-table')
        elif section_type == 'diagnosis':
            if isinstance(content, list) and content:
                section_html += self._generate_html_diagnosis_table(content)
            else:
                section_html += self._generate_html_table(content, ['项目', '值'], 'info-table')
        elif section_type == 'features':
            section_html += self._generate_html_table(content, ['特征类型', '特征值'], 'features-table')
        elif section_type == 'file_list':
            if isinstance(content, list) and content:
                section_html += self._generate_html_file_list_table(content)
        
        section_html += '</div>'
        return section_html
    
    def _generate_html_table(self, content: Dict, headers: list, css_class: str) -> str:
        """生成HTML表格"""
        if not content:
            return ""
        
        html = f'<table class="{css_class}"><thead><tr>'
        for header in headers:
            html += f'<th>{header}</th>'
        html += '</tr></thead><tbody>'
        
        for key, value in content.items():
            html += f'<tr><td>{key}</td><td>{value}</td></tr>'
        
        html += '</tbody></table>'
        return html
    
    def _generate_html_diagnosis_table(self, content: list) -> str:
        """生成诊断结果HTML表格"""
        if not content:
            return ""
        
        headers = ['算法类型', '算法名称', '故障类型', '置信度', '诊断状态', '诊断时间']
        html = '<table class="diagnosis-table"><thead><tr>'
        for header in headers:
            html += f'<th>{header}</th>'
        html += '</tr></thead><tbody>'
        
        for item in content:
            html += '<tr>'
            for header in headers:
                value = item.get(header, '')
                html += f'<td>{value}</td>'
            html += '</tr>'
        
        html += '</tbody></table>'
        return html
    
    def _generate_html_file_list_table(self, content: list) -> str:
        """生成文件列表HTML表格"""
        if not content:
            return ""
        
        headers = ['序号', '文件名', '车型', '部件', '测试时间']
        html = '<table class="diagnosis-table"><thead><tr>'
        for header in headers:
            html += f'<th>{header}</th>'
        html += '</tr></thead><tbody>'
        
        for item in content:
            html += '<tr>'
            for header in headers:
                value = item.get(header, '')
                html += f'<td>{value}</td>'
            html += '</tr>'
        
        html += '</tbody></table>'
        return html
    
    def _prepare_table_data(self, content: Dict) -> list:
        """准备表格数据"""
        if not content:
            return []
        
        table_data = [['项目', '值']]
        for key, value in content.items():
            table_data.append([str(key), str(value)])
        
        return table_data
    
    def _get_chart_title(self, chart_name: str) -> str:
        """获取图表标题"""
        title_map = {
            'diagnosis_summary': '诊断结果分布',
            'confidence_trend': '置信度趋势',
            'algorithm_performance': '算法性能对比',
            'feature_distribution': '特征分布'
        }
        return title_map.get(chart_name, chart_name)
