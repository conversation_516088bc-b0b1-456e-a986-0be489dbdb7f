"""
PyQt5修复脚本 - 解决QMimeDatabase错误
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {description}成功")
            return True
        else:
            print(f"❌ {description}失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ {description}出错: {e}")
        return False

def fix_pyqt5():
    """修复PyQt5安装"""
    print("🚀 开始修复PyQt5安装...")
    
    # 方法1: 重新安装PyQt5
    print("\n📦 方法1: 重新安装PyQt5")
    commands = [
        ("pip uninstall PyQt5 -y", "卸载PyQt5"),
        ("pip uninstall PyQt5-Qt5 -y", "卸载PyQt5-Qt5"),
        ("pip uninstall PyQt5-sip -y", "卸载PyQt5-sip"),
        ("pip install PyQt5==5.15.7", "安装PyQt5 5.15.7"),
        ("pip install PyQt5-tools", "安装PyQt5-tools")
    ]
    
    success = True
    for command, description in commands:
        if not run_command(command, description):
            success = False
    
    if success:
        print("✅ PyQt5重新安装完成")
    else:
        print("⚠️ PyQt5重新安装可能有问题，尝试方法2")
        
        # 方法2: 使用conda安装
        print("\n📦 方法2: 使用conda安装PyQt5")
        conda_commands = [
            ("conda uninstall pyqt -y", "卸载conda PyQt"),
            ("conda install pyqt=5.15.7 -y", "安装conda PyQt 5.15.7")
        ]
        
        for command, description in conda_commands:
            run_command(command, description)

def test_pyqt5():
    """测试PyQt5是否正常工作"""
    print("\n🧪 测试PyQt5...")
    
    test_script = '''
import sys
import os

# 设置Qt环境变量
os.environ['QT_QPA_PLATFORM'] = 'windows'
os.environ['QT_LOGGING_RULES'] = '*=false'

try:
    from PyQt5.QtWidgets import QApplication, QLabel, QWidget
    from PyQt5.QtCore import Qt
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = QWidget()
    window.setWindowTitle("PyQt5测试")
    window.setGeometry(100, 100, 300, 200)
    
    label = QLabel("PyQt5工作正常！", window)
    label.setAlignment(Qt.AlignCenter)
    label.setGeometry(50, 80, 200, 40)
    
    window.show()
    
    print("✅ PyQt5测试成功！")
    print("窗口将在3秒后关闭...")
    
    # 3秒后关闭
    from PyQt5.QtCore import QTimer
    timer = QTimer()
    timer.timeout.connect(app.quit)
    timer.start(3000)
    
    app.exec_()
    
except Exception as e:
    print(f"❌ PyQt5测试失败: {e}")
    sys.exit(1)
'''
    
    # 将测试脚本写入临时文件
    with open('test_pyqt5_temp.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    # 运行测试
    success = run_command("python test_pyqt5_temp.py", "PyQt5功能测试")
    
    # 清理临时文件
    try:
        os.remove('test_pyqt5_temp.py')
    except:
        pass
    
    return success

def main():
    """主函数"""
    print("🔧 PyQt5修复工具")
    print("=" * 50)
    
    # 修复PyQt5
    fix_pyqt5()
    
    # 测试PyQt5
    if test_pyqt5():
        print("\n🎉 PyQt5修复成功！")
        print("现在可以尝试运行主应用程序:")
        print("  python main.py")
        print("  或者")
        print("  python start_app.py")
    else:
        print("\n❌ PyQt5修复失败")
        print("请尝试以下手动步骤:")
        print("1. 完全卸载Python和Anaconda")
        print("2. 重新安装Anaconda")
        print("3. 创建新的conda环境")
        print("4. 重新安装所有依赖项")

if __name__ == "__main__":
    main()
