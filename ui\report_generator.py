"""
检测报告生成页面
基于现有功能生成综合检测报告
"""

import os
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QDateEdit, QTextEdit, QScrollArea, QFrame,
    QGroupBox, QTableWidget, QTableWidgetItem, QHeaderView,
    QProgressBar, QMessageBox, QFileDialog, QSplitter,
    QCheckBox, QListWidget, QListWidgetItem, QSizePolicy
)
from PyQt5.QtCore import Qt, QDate, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

from ui.styles import (
    PRIMARY_BG, SECONDARY_BG, ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY,
    SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, INFO_COLOR, HIGHLIGHT_COLOR
)
from ui.report_data_model import (
    ReportData, FileInfo, FeatureResult, DiagnosisResult, AnalysisSummary,
    SingleFileReportTemplate, ComparativeReportTemplate, ReferenceFormatReportTemplate, ReportDataValidator
)
from ui.report_content_generator import ReportContentGenerator
from ui.report_exporter import ReportExporter
from database.db_manager import DatabaseManager


class ReportGenerationThread(QThread):
    """报告生成线程"""

    progress_updated = pyqtSignal(int, str)
    report_generated = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, db_manager, report_config):
        super().__init__()
        self.db_manager = db_manager
        self.report_config = report_config

    def safe_parse_datetime(self, date_value):
        """安全地将日期字符串转换为datetime对象"""
        if isinstance(date_value, datetime):
            return date_value
        elif isinstance(date_value, str):
            try:
                # 尝试多种日期格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y/%m/%d %H:%M:%S',
                    '%Y-%m-%d',
                    '%Y/%m/%d',
                    '%Y年%m月%d日 %H:%M:%S',
                    '%Y年%m月%d日'
                ]
                for fmt in formats:
                    try:
                        return datetime.strptime(date_value, fmt)
                    except ValueError:
                        continue
                # 如果所有格式都失败，返回当前时间
                return datetime.now()
            except:
                return datetime.now()
        else:
            return datetime.now()
    
    def run(self):
        """执行报告生成"""
        try:
            self.progress_updated.emit(10, "正在获取文件信息...")

            # 获取文件ID列表
            file_ids = self.report_config.get('file_ids', [])
            if not file_ids:
                self.error_occurred.emit("未选择任何文件")
                return
            
            report_data_list = []
            
            for i, file_id in enumerate(file_ids):
                progress = 20 + (i * 50 // len(file_ids))
                self.progress_updated.emit(progress, f"正在处理文件 {i+1}/{len(file_ids)}...")
                
                # 获取文件信息
                file_info_data = self.db_manager.get_file_info(file_id)
                if not file_info_data:
                    continue
                
                file_info = FileInfo(
                    file_id=file_info_data['ID'],
                    file_name=file_info_data['TDMS文件名'],
                    file_path=file_info_data['TDMS文件路径'],
                    test_time=self.safe_parse_datetime(file_info_data['测试时间']),
                    vehicle_type=file_info_data['车型'],
                    component=file_info_data['部件'],
                    sensor_type=file_info_data['传感器类型'],
                    sensor_number=file_info_data['传感器编号'],
                    sampling_rate=48000  # 默认采样率
                )
                
                # 获取诊断结果
                diagnosis_results = []
                try:
                    diagnosis_data = self.db_manager.get_diagnosis_results(
                        file_id=file_id,
                        start_date=self.report_config.get('start_date'),
                        end_date=self.report_config.get('end_date')
                    )
                    
                    for diag in diagnosis_data:
                        diagnosis_result = DiagnosisResult(
                            algorithm_type=diag['algorithm_type'],
                            algorithm_name=diag['algorithm_name'],
                            fault_type=diag['fault_type'],
                            confidence=float(diag['confidence']) if diag['confidence'] else 0.0,
                            status=diag['status'],
                            diagnosis_time=self.safe_parse_datetime(diag['diagnosis_time']),
                            details=json.loads(diag['details']) if diag['details'] else None
                        )
                        diagnosis_results.append(diagnosis_result)
                except:
                    # 如果没有诊断数据，创建模拟数据
                    diagnosis_result = DiagnosisResult(
                        algorithm_type='classical',
                        algorithm_name='SVM分类器',
                        fault_type='正常',
                        confidence=0.85,
                        status='normal',
                        diagnosis_time=datetime.now(),
                        details=None
                    )
                    diagnosis_results.append(diagnosis_result)
                
                # 获取特征提取结果
                feature_results = []
                try:
                    feature_data = self.db_manager.get_feature_extraction_results(file_id=file_id)
                    
                    for feat in feature_data:
                        feature_result = FeatureResult(
                            feature_type=feat['feature_type'],
                            feature_name=feat['feature_name'],
                            feature_value=float(feat['feature_value']) if feat['feature_value'] else 0.0,
                            extraction_time=self.safe_parse_datetime(feat['extraction_time']),
                            parameters=json.loads(feat['parameters']) if feat['parameters'] else None
                        )
                        feature_results.append(feature_result)
                except:
                    # 如果没有特征数据，创建模拟数据
                    feature_result = FeatureResult(
                        feature_type='时域特征',
                        feature_name='RMS',
                        feature_value=2.45,
                        extraction_time=datetime.now(),
                        parameters=None
                    )
                    feature_results.append(feature_result)
                
                # 获取分析摘要
                analysis_summary = AnalysisSummary(
                    total_diagnoses=len(diagnosis_results),
                    normal_count=len([d for d in diagnosis_results if d.status == 'normal']),
                    warning_count=len([d for d in diagnosis_results if d.status == 'warning']),
                    fault_count=len([d for d in diagnosis_results if d.status == 'fault']),
                    last_diagnosis=diagnosis_results[-1].diagnosis_time if diagnosis_results else None,
                    avg_confidence=sum(d.confidence for d in diagnosis_results) / len(diagnosis_results) if diagnosis_results else 0,
                    total_features=len(feature_results),
                    feature_types=len(set(f.feature_type for f in feature_results)),
                    last_extraction=feature_results[-1].extraction_time if feature_results else None
                )
                
                # 创建报告数据
                report_data = ReportData(
                    file_info=file_info,
                    feature_results=feature_results,
                    diagnosis_results=diagnosis_results,
                    analysis_summary=analysis_summary
                )
                
                report_data_list.append(report_data)
            
            self.progress_updated.emit(80, "正在生成报告内容...")

            # 生成报告内容
            report_type = self.report_config.get('report_type', 'single')
            template_type = 'reference'  # 只使用参考格式

            if report_type == 'single' and report_data_list:
                # 只使用参考格式模板
                template = ReferenceFormatReportTemplate()
                report_content = template.generate_content(report_data_list[0])

                # 生成图表
                self.progress_updated.emit(90, "正在生成图表...")
                charts = self.generate_charts_for_single_report(report_data_list[0])

            elif report_type == 'comparative':
                template = ComparativeReportTemplate()
                report_content = template.generate_content(report_data_list)

                # 生成对比图表
                self.progress_updated.emit(90, "正在生成对比图表...")
                charts = self.generate_charts_for_comparative_report(report_data_list)

            else:
                self.error_occurred.emit("无效的报告类型或无数据")
                return

            # 将图表添加到报告内容
            result = {
                'report_content': report_content,
                'charts': charts
            }

            self.progress_updated.emit(100, "报告生成完成")
            self.report_generated.emit(result)
            
        except Exception as e:
            import traceback
            error_msg = f"报告生成失败: {str(e)}\n{traceback.format_exc()}"
            self.error_occurred.emit(error_msg)

    def generate_charts_for_single_report(self, report_data: ReportData) -> Dict:
        """为单文件报告生成图表"""
        charts = {}
        content_generator = ReportContentGenerator()

        try:
            # 生成诊断摘要图表
            if report_data.diagnosis_results:
                charts['diagnosis_summary'] = content_generator.generate_diagnosis_summary_chart(report_data)
                charts['confidence_trend'] = content_generator.generate_confidence_trend_chart(report_data)
                charts['algorithm_performance'] = content_generator.generate_algorithm_performance_chart(report_data)

            # 生成特征分布图表
            if report_data.feature_results:
                charts['feature_distribution'] = content_generator.generate_feature_distribution_chart(report_data)

        except Exception as e:
            print(f"图表生成失败: {e}")

        return charts

    def generate_charts_for_comparative_report(self, report_data_list: List[ReportData]) -> Dict:
        """为对比报告生成图表"""
        charts = {}

        try:
            # 这里可以添加对比图表的生成逻辑
            # 例如：多文件诊断结果对比、特征对比等
            pass
        except Exception as e:
            print(f"对比图表生成失败: {e}")

        return charts


class ReportGenerator(QWidget):
    """检测报告生成页面"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
        self.current_report = None
        self.current_charts = None
        self.content_generator = ReportContentGenerator()
        self.exporter = ReportExporter()
        self.init_ui()
        self.load_available_files()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("📋 检测报告生成")
        title_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        title_label.setMaximumHeight(40)
        layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧配置面板
        config_panel = self.create_config_panel()
        config_panel.setMaximumWidth(350)
        splitter.addWidget(config_panel)
        
        # 右侧预览面板
        preview_panel = self.create_preview_panel()
        splitter.addWidget(preview_panel)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 0)  # 配置面板固定宽度
        splitter.setStretchFactor(1, 1)  # 预览面板可伸缩
        
        layout.addWidget(splitter)

    def create_config_panel(self) -> QWidget:
        """创建配置面板"""
        panel = QFrame()
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                padding: 10px;
            }}
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 报告类型选择
        type_group = QGroupBox("报告类型")
        type_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        type_layout = QVBoxLayout(type_group)

        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems(["单文件分析报告", "多文件对比报告"])
        self.report_type_combo.setStyleSheet(f"""
            QComboBox {{
                font-size: 12px;
                padding: 8px;
                border: 1px solid {ACCENT_COLOR};
                border-radius: 4px;
                background-color: white;
            }}
        """)
        type_layout.addWidget(self.report_type_combo)

        # 模板类型选择 - 只保留参考格式
        self.template_type_combo = QComboBox()
        self.template_type_combo.addItems(["参考格式"])
        self.template_type_combo.setStyleSheet(f"""
            QComboBox {{
                font-size: 12px;
                padding: 8px;
                border: 1px solid {ACCENT_COLOR};
                border-radius: 4px;
                background-color: white;
            }}
        """)
        type_layout.addWidget(QLabel("模板格式:"))
        type_layout.addWidget(self.template_type_combo)

        layout.addWidget(type_group)

        # 文件选择
        file_group = QGroupBox("文件选择")
        file_group.setStyleSheet(f"""
            QGroupBox {{
                font-size: 14px;
                font-weight: bold;
                color: {TEXT_PRIMARY};
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }}
        """)

        file_layout = QVBoxLayout(file_group)

        self.file_list = QListWidget()
        self.file_list.setSelectionMode(QListWidget.MultiSelection)
        self.file_list.setStyleSheet(f"""
            QListWidget {{
                font-size: 12px;
                border: 1px solid {ACCENT_COLOR};
                border-radius: 4px;
                background-color: white;
                min-height: 150px;
            }}
            QListWidget::item {{
                padding: 5px;
                border-bottom: 1px solid #eee;
            }}
            QListWidget::item:selected {{
                background-color: {HIGHLIGHT_COLOR};
                color: white;
            }}
        """)
        file_layout.addWidget(self.file_list)

        layout.addWidget(file_group)

        # 生成按钮
        self.generate_btn = QPushButton("🔄 生成报告")
        self.generate_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ACCENT_COLOR};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 14px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
            QPushButton:pressed {{
                background-color: #004080;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        self.generate_btn.clicked.connect(self.generate_report)
        layout.addWidget(self.generate_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ACCENT_COLOR};
                border-radius: 5px;
                text-align: center;
                font-size: 12px;
            }}
            QProgressBar::chunk {{
                background-color: {SUCCESS_COLOR};
                border-radius: 3px;
            }}
        """)
        layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet(f"""
            font-size: 12px;
            color: {TEXT_SECONDARY};
            padding: 5px;
        """)
        layout.addWidget(self.status_label)

        layout.addStretch()

        return panel

    def create_preview_panel(self) -> QWidget:
        """创建预览面板"""
        panel = QFrame()
        panel.setStyleSheet(f"""
            QFrame {{
                background-color: {SECONDARY_BG};
                border-radius: 8px;
                padding: 10px;
            }}
        """)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 预览标题
        preview_title = QLabel("📄 报告预览")
        preview_title.setStyleSheet(f"""
            font-size: 18px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
        """)
        layout.addWidget(preview_title)

        # 预览区域
        self.preview_area = QScrollArea()
        self.preview_area.setWidgetResizable(True)
        self.preview_area.setStyleSheet(f"""
            QScrollArea {{
                border: 1px solid {ACCENT_COLOR};
                border-radius: 4px;
                background-color: white;
            }}
            QScrollBar:vertical {{
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }}
            QScrollBar::handle:vertical {{
                background-color: {ACCENT_COLOR};
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {HIGHLIGHT_COLOR};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
                border: none;
                background: none;
            }}
        """)

        # 默认预览内容
        default_preview = QLabel("请选择文件并生成报告以查看预览")
        default_preview.setAlignment(Qt.AlignCenter)
        default_preview.setStyleSheet(f"""
            font-size: 14px;
            color: {TEXT_SECONDARY};
            padding: 50px;
        """)
        self.preview_area.setWidget(default_preview)

        layout.addWidget(self.preview_area)

        # 导出按钮
        export_layout = QHBoxLayout()

        self.export_pdf_btn = QPushButton("📄 导出PDF")
        self.export_pdf_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ERROR_COLOR};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 15px;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #e74c3c;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        self.export_pdf_btn.clicked.connect(self.export_pdf)
        self.export_pdf_btn.setEnabled(False)

        self.export_html_btn = QPushButton("🌐 导出HTML")
        self.export_html_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {INFO_COLOR};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 15px;
                font-size: 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: #3498db;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """)
        self.export_html_btn.clicked.connect(self.export_html)
        self.export_html_btn.setEnabled(False)

        export_layout.addWidget(self.export_pdf_btn)
        export_layout.addWidget(self.export_html_btn)
        export_layout.addStretch()

        layout.addLayout(export_layout)

        return panel

    def load_available_files(self):
        """加载可用文件列表"""
        try:
            files = self.db_manager.get_tdms_files()
            self.file_list.clear()

            for file_info in files:
                item_text = f"{file_info['车型']} - {file_info['部件']} - {file_info['TDMS文件名']}"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, file_info['ID'])  # 存储文件ID
                self.file_list.addItem(item)

            self.status_label.setText(f"已加载 {len(files)} 个文件")

        except Exception as e:
            self.status_label.setText(f"加载文件列表失败: {str(e)}")
            self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 12px;")

    def generate_report(self):
        """生成报告"""
        # 获取选中的文件
        selected_items = self.file_list.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "警告", "请至少选择一个文件")
            return

        # 获取文件ID列表
        file_ids = [item.data(Qt.UserRole) for item in selected_items]

        # 获取报告类型
        report_type = 'single' if self.report_type_combo.currentText() == "单文件分析报告" else 'comparative'
        template_type = 'reference'  # 只使用参考格式

        # 验证选择
        if report_type == 'single' and len(file_ids) > 1:
            QMessageBox.warning(self, "警告", "单文件分析报告只能选择一个文件")
            return

        if report_type == 'comparative' and len(file_ids) < 2:
            QMessageBox.warning(self, "警告", "对比分析报告至少需要选择两个文件")
            return

        # 构建报告配置
        report_config = {
            'file_ids': file_ids,
            'report_type': report_type,
            'template_type': template_type,
            'start_date': None,
            'end_date': None
        }

        # 开始生成报告
        self.start_report_generation(report_config)

    def start_report_generation(self, report_config: Dict):
        """开始报告生成"""
        self.generate_btn.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.status_label.setText("正在生成报告...")

        # 创建并启动生成线程
        self.generation_thread = ReportGenerationThread(self.db_manager, report_config)
        self.generation_thread.progress_updated.connect(self.on_progress_updated)
        self.generation_thread.report_generated.connect(self.on_report_generated)
        self.generation_thread.error_occurred.connect(self.on_generation_error)
        self.generation_thread.start()

    def on_progress_updated(self, progress: int, message: str):
        """进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)

    def on_report_generated(self, result: Dict):
        """报告生成完成"""
        self.current_report = result['report_content']
        self.current_charts = result['charts']

        # 更新预览
        self.update_preview(self.current_report)

        # 启用导出按钮
        self.export_pdf_btn.setEnabled(True)
        self.export_html_btn.setEnabled(True)

        # 重置UI状态
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("报告生成完成")
        self.status_label.setStyleSheet(f"color: {SUCCESS_COLOR}; font-size: 12px;")

    def on_generation_error(self, error_message: str):
        """报告生成错误"""
        QMessageBox.critical(self, "错误", f"报告生成失败:\n{error_message}")

        # 重置UI状态
        self.generate_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText(f"生成失败: {error_message}")
        self.status_label.setStyleSheet(f"color: {ERROR_COLOR}; font-size: 12px;")

    def update_preview(self, report_content: Dict):
        """更新报告预览"""
        preview_widget = QWidget()
        preview_widget.setStyleSheet("background-color: white;")
        layout = QVBoxLayout(preview_widget)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)

        # 报告标题
        title_label = QLabel(report_content.get('title', '检测报告'))
        title_label.setStyleSheet(f"""
            font-size: 28px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            text-align: center;
            margin-bottom: 20px;
            font-family: 'Microsoft YaHei';
            padding: 15px;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)

        # 副标题
        if report_content.get('subtitle'):
            subtitle_label = QLabel(report_content.get('subtitle'))
            subtitle_label.setStyleSheet(f"""
                font-size: 18px;
                color: {TEXT_SECONDARY};
                text-align: center;
                margin-bottom: 15px;
                font-family: 'Microsoft YaHei';
                padding: 10px;
            """)
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setWordWrap(True)
            layout.addWidget(subtitle_label)

        # 生成时间
        if report_content.get('generated_time'):
            time_label = QLabel(f"生成时间: {report_content.get('generated_time')}")
            time_label.setStyleSheet(f"""
                font-size: 14px;
                color: {TEXT_SECONDARY};
                text-align: center;
                margin-bottom: 25px;
                font-family: 'Microsoft YaHei';
                padding: 8px;
            """)
            time_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(time_label)

        # 报告章节
        for section in report_content.get('sections', []):
            section_widget = self.create_section_widget(section)
            layout.addWidget(section_widget)

        # 添加底部间距
        layout.addSpacing(50)

        # 设置widget的最小大小
        preview_widget.setMinimumWidth(600)

        # 确保预览组件可见
        preview_widget.setVisible(True)
        preview_widget.show()

        self.preview_area.setWidget(preview_widget)

        # 强制显示所有组件 - 关键修复
        preview_widget.setVisible(True)
        preview_widget.show()

        # 递归显示所有子组件
        def force_show_all_widgets(widget):
            widget.setVisible(True)
            widget.show()
            for child in widget.findChildren(QWidget):
                child.setVisible(True)
                child.show()

        force_show_all_widgets(preview_widget)

        # 确保预览区域显示
        self.preview_area.setVisible(True)
        self.preview_area.show()
        self.preview_area.update()
        self.preview_area.repaint()

    def create_section_widget(self, section: Dict) -> QWidget:
        """创建章节组件"""
        section_frame = QFrame()
        section_frame.setStyleSheet(f"""
            QFrame {{
                background-color: #fafafa;
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                margin: 10px 0;
                padding: 20px;
            }}
        """)

        layout = QVBoxLayout(section_frame)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 确保章节框架不限制内容高度 - 关键修复
        section_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)
        section_frame.setMinimumHeight(50)  # 设置最小高度

        # 章节标题
        if section.get('title'):
            title_label = QLabel(section['title'])
            title_label.setStyleSheet(f"""
                font-size: 20px;
                font-weight: bold;
                color: {ACCENT_COLOR};
                margin-bottom: 15px;
                font-family: 'Microsoft YaHei';
                padding: 10px;
                background-color: #f0f8ff;
                border-radius: 4px;
            """)
            title_label.setWordWrap(True)
            layout.addWidget(title_label)

        # 章节内容
        section_type = section.get('type', '')
        content = section.get('content', {})

        if section_type == 'info' or section_type == 'reference_format':
            content_widget = self.create_info_content(content)
        elif section_type == 'summary':
            content_widget = self.create_summary_content(content)
        elif section_type == 'diagnosis':
            content_widget = self.create_diagnosis_content(content)
        elif section_type == 'features':
            content_widget = self.create_features_content(content)
        elif section_type == 'file_list':
            content_widget = self.create_file_list_content(content)
        elif section_type == 'comparison':
            content_widget = self.create_comparison_content(content)
        else:
            content_widget = QLabel("未知章节类型")
            content_widget.setStyleSheet(f"""
                font-size: 14px;
                color: {TEXT_SECONDARY};
                padding: 20px;
                text-align: center;
            """)

        layout.addWidget(content_widget)

        # 确保组件可见
        section_frame.setVisible(True)
        content_widget.setVisible(True)

        return section_frame

    def create_info_content(self, content: Dict) -> QWidget:
        """创建信息内容组件"""
        table = QTableWidget()
        table.setColumnCount(2)
        table.setHorizontalHeaderLabels(["项目", "值"])
        table.setRowCount(len(content))

        for i, (key, value) in enumerate(content.items()):
            table.setItem(i, 0, QTableWidgetItem(str(key)))
            table.setItem(i, 1, QTableWidgetItem(str(value)))

        # 设置表格样式 - 修复文字颜色问题
        table.setStyleSheet(f"""
            QTableWidget {{
                gridline-color: #ddd;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: black;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border-bottom: 1px solid #eee;
                font-size: 14px;
                color: black;
                background-color: white;
            }}
            QTableWidget::item:selected {{
                background-color: #e3f2fd;
                color: black;
            }}
            QTableWidget::item:alternate {{
                background-color: #f9f9f9;
                color: black;
            }}
        """)

        # 调整列宽
        header = table.horizontalHeader()
        header.setStretchLastSection(True)
        table.setColumnWidth(0, 150)

        # 调整行高和表格大小
        table.verticalHeader().setVisible(False)
        table.setAlternatingRowColors(True)

        # 设置表格高度以显示所有行
        row_height = 40  # 每行高度
        header_height = 35  # 表头高度
        total_height = header_height + (len(content) * row_height) + 10  # 额外边距
        table.setMinimumHeight(total_height)
        table.setMaximumHeight(total_height)

        # 调整每行高度
        for i in range(len(content)):
            table.setRowHeight(i, row_height)

        # 设置大小策略
        table.setSizePolicy(table.sizePolicy().Expanding, table.sizePolicy().Fixed)

        # 禁用滚动条（因为我们已经设置了固定高度）
        table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 确保表格可见
        table.setVisible(True)

        return table

    def create_summary_content(self, content: Dict) -> QWidget:
        """创建摘要内容组件"""
        return self.create_info_content(content)

    def create_diagnosis_content(self, content) -> QWidget:
        """创建诊断内容组件"""
        if not content:
            return QLabel("无诊断数据")

        # 如果内容是字典，转换为信息表格
        if isinstance(content, dict):
            return self.create_info_content(content)

        # 如果内容不是列表，创建错误提示
        if not isinstance(content, list):
            error_label = QLabel("诊断内容格式错误")
            error_label.setStyleSheet(f"""
                font-size: 14px;
                color: {TEXT_SECONDARY};
                padding: 20px;
                text-align: center;
            """)
            return error_label

        table = QTableWidget()

        # 根据第一个项目的键来确定表头
        if content and isinstance(content[0], dict):
            headers = list(content[0].keys())
        else:
            headers = ['算法类型', '算法名称', '故障类型', '置信度', '诊断状态', '诊断时间']

        table.setColumnCount(len(headers))
        table.setHorizontalHeaderLabels(headers)
        table.setRowCount(len(content))

        for i, item in enumerate(content):
            if isinstance(item, dict):
                for j, header in enumerate(headers):
                    value = item.get(header, '')
                    table.setItem(i, j, QTableWidgetItem(str(value)))
            else:
                # 如果项目不是字典，显示为字符串
                table.setItem(i, 0, QTableWidgetItem(str(item)))

        # 设置表格样式 - 修复文字颜色问题
        table.setStyleSheet(f"""
            QTableWidget {{
                gridline-color: #ddd;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: black;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border-bottom: 1px solid #eee;
                font-size: 14px;
                color: black;
                background-color: white;
            }}
            QTableWidget::item:selected {{
                background-color: #e3f2fd;
                color: black;
            }}
            QTableWidget::item:alternate {{
                background-color: #f9f9f9;
                color: black;
            }}
        """)

        # 调整列宽 - 确保所有内容可见
        header = table.horizontalHeader()
        header.setStretchLastSection(True)

        # 设置各列的最小宽度
        if table.columnCount() >= 6:
            table.setColumnWidth(0, 120)  # 算法类型
            table.setColumnWidth(1, 120)  # 算法名称
            table.setColumnWidth(2, 80)   # 诊断结果
            table.setColumnWidth(3, 80)   # 置信度
            table.setColumnWidth(4, 80)   # 状态评估
            table.setColumnWidth(5, 150)  # 诊断时间

        table.verticalHeader().setVisible(False)
        table.setAlternatingRowColors(True)

        # 设置表格高度以显示所有行 - 关键修复
        row_height = 45  # 增加每行高度以确保内容可见
        header_height = 40  # 增加表头高度
        total_height = header_height + (len(content) * row_height) + 20  # 增加额外边距
        table.setMinimumHeight(total_height)
        table.setMaximumHeight(total_height)

        # 调整每行高度
        for i in range(len(content)):
            table.setRowHeight(i, row_height)

        # 设置大小策略和最小宽度
        table.setSizePolicy(table.sizePolicy().Expanding, table.sizePolicy().Fixed)

        # 设置表格最小宽度以确保所有列都可见
        min_width = sum([120, 120, 80, 80, 80, 150]) + 50  # 列宽总和 + 边距
        table.setMinimumWidth(min_width)

        # 确保表格有足够的显示空间
        table.setMinimumSize(min_width, total_height)
        table.updateGeometry()  # 强制更新几何布局

        # 禁用垂直滚动条，但保留水平滚动条以防内容过宽
        table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 确保表格可见
        table.setVisible(True)

        # 创建容器来确保表格正确显示
        container = QWidget()
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        container_layout.setSpacing(0)
        container_layout.addWidget(table)

        # 设置容器大小策略
        container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        container.setMinimumHeight(total_height + 10)
        container.setMaximumHeight(total_height + 10)

        return container

    def create_features_content(self, content) -> QWidget:
        """创建特征内容组件"""
        # 检查内容类型，如果是字典则转换为信息表格，如果是列表则使用诊断表格
        if isinstance(content, dict):
            return self.create_info_content(content)
        elif isinstance(content, list):
            return self.create_diagnosis_content(content)
        else:
            # 如果是其他类型，创建一个错误提示
            error_label = QLabel("特征内容格式错误")
            error_label.setStyleSheet(f"""
                font-size: 14px;
                color: {TEXT_SECONDARY};
                padding: 20px;
                text-align: center;
            """)
            return error_label

    def create_file_list_content(self, content: List[Dict]) -> QWidget:
        """创建文件列表内容组件"""
        return self.create_diagnosis_content(content)

    def create_comparison_content(self, content: Dict) -> QWidget:
        """创建对比内容组件"""
        return self.create_info_content(content)

    def export_pdf(self):
        """导出PDF"""
        if not self.current_report:
            QMessageBox.warning(self, "警告", "请先生成报告")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存PDF报告",
            f"检测报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            "PDF文件 (*.pdf)"
        )

        if file_path:
            try:
                self.save_pdf_report(file_path)
                QMessageBox.information(self, "成功", f"PDF报告已保存到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"PDF导出失败:\n{str(e)}")

    def export_html(self):
        """导出HTML"""
        if not self.current_report:
            QMessageBox.warning(self, "警告", "请先生成报告")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存HTML报告",
            f"检测报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
            "HTML文件 (*.html)"
        )

        if file_path:
            try:
                self.save_html_report(file_path)
                QMessageBox.information(self, "成功", f"HTML报告已保存到:\n{file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"HTML导出失败:\n{str(e)}")

    def save_pdf_report(self, file_path: str):
        """保存PDF报告"""
        try:
            self.exporter.export_to_pdf(self.current_report, file_path, self.current_charts)
        except ImportError as e:
            raise ImportError("PDF导出需要安装reportlab库。请运行: pip install reportlab")
        except Exception as e:
            raise Exception(f"PDF导出失败: {str(e)}")

    def save_html_report(self, file_path: str):
        """保存HTML报告"""
        try:
            self.exporter.export_to_html(self.current_report, file_path, self.current_charts)
        except Exception as e:
            raise Exception(f"HTML导出失败: {str(e)}")
