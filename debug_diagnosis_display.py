"""
调试诊断结果显示问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_diagnosis_section():
    """调试诊断结果章节"""
    print("🔍 调试变速箱故障诊断结果章节...")
    
    try:
        from ui.report_data_model import (
            ReferenceFormatReportTemplate, ReportData, FileInfo,
            FeatureResult, DiagnosisResult, AnalysisSummary
        )
        
        # 创建真实的测试数据
        file_info = FileInfo(
            file_id=1,
            file_name="变速箱测试数据.tdms",
            file_path="D:\\test\\",
            test_time=datetime.now(),
            vehicle_type="重型卡车",
            component="变速箱",
            sensor_type="声音传感器",
            sensor_number="316F10",
            sampling_rate=48000
        )
        
        # 创建诊断结果
        diagnosis_results = [
            DiagnosisResult("classical", "SVM分类器", "正常", 0.85, "normal", datetime.now()),
            DiagnosisResult("deep_learning", "CNN模型", "正常", 0.92, "normal", datetime.now()),
            DiagnosisResult("classical", "随机森林", "正常", 0.88, "normal", datetime.now())
        ]
        
        feature_results = [
            FeatureResult("时域特征", "RMS", 2.45, datetime.now()),
            FeatureResult("频域特征", "主频", 1200.5, datetime.now())
        ]
        
        analysis_summary = AnalysisSummary(3, 3, 0, 0, datetime.now(), 0.883, 2, 2, datetime.now())
        
        report_data = ReportData(
            file_info=file_info,
            feature_results=feature_results,
            diagnosis_results=diagnosis_results,
            analysis_summary=analysis_summary
        )
        
        # 生成报告内容
        template = ReferenceFormatReportTemplate()
        report_content = template.generate_content(report_data)
        
        print(f"✓ 报告内容生成成功")
        print(f"  标题: {report_content['title']}")
        print(f"  章节数: {len(report_content['sections'])}")
        
        # 详细检查诊断结果章节
        for i, section in enumerate(report_content['sections']):
            title = section.get('title', f'章节{i+1}')
            section_type = section.get('type', '未知')
            content = section.get('content', {})
            
            print(f"\n📋 章节 {i+1}: {title} (类型: {section_type})")
            
            if title == '变速箱故障诊断结果':
                print(f"  内容类型: {type(content)}")
                print(f"  内容长度: {len(content) if hasattr(content, '__len__') else 'N/A'}")
                
                if isinstance(content, list):
                    print(f"  列表内容:")
                    for j, item in enumerate(content):
                        print(f"    项目 {j+1}: {type(item)}")
                        if isinstance(item, dict):
                            print(f"      键: {list(item.keys())}")
                            for key, value in item.items():
                                print(f"      {key}: {value}")
                        else:
                            print(f"      值: {item}")
                        print()
                elif isinstance(content, dict):
                    print(f"  字典内容:")
                    for key, value in content.items():
                        print(f"    {key}: {value}")
                else:
                    print(f"  其他内容: {content}")
        
        return report_content
        
    except Exception as e:
        print(f"✗ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def debug_preview_display():
    """调试预览显示"""
    print("\n🖥️ 调试预览显示...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 获取报告内容
        report_content = debug_diagnosis_section()
        if not report_content:
            return False
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        # 找到诊断结果章节
        diagnosis_section = None
        for section in report_content['sections']:
            if section.get('title') == '变速箱故障诊断结果':
                diagnosis_section = section
                break
        
        if diagnosis_section:
            print(f"找到诊断结果章节:")
            print(f"  标题: {diagnosis_section['title']}")
            print(f"  类型: {diagnosis_section['type']}")
            content = diagnosis_section['content']
            print(f"  内容类型: {type(content)}")
            
            # 测试创建诊断内容组件
            try:
                diagnosis_widget = generator.create_diagnosis_content(content)
                print(f"✓ 诊断内容组件创建成功")
                print(f"  组件类型: {type(diagnosis_widget)}")
                
                # 如果是表格，检查表格属性
                if hasattr(diagnosis_widget, 'rowCount'):
                    print(f"  表格行数: {diagnosis_widget.rowCount()}")
                    print(f"  表格列数: {diagnosis_widget.columnCount()}")
                    print(f"  最小高度: {diagnosis_widget.minimumHeight()}")
                    print(f"  最大高度: {diagnosis_widget.maximumHeight()}")
                    
                    # 检查表格内容
                    for row in range(min(3, diagnosis_widget.rowCount())):  # 只检查前3行
                        row_data = []
                        for col in range(diagnosis_widget.columnCount()):
                            item = diagnosis_widget.item(row, col)
                            if item:
                                row_data.append(item.text())
                            else:
                                row_data.append("空")
                        print(f"  行 {row+1}: {row_data}")
                
            except Exception as e:
                print(f"✗ 诊断内容组件创建失败: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            print("✗ 未找到诊断结果章节")
            return False
        
        # 测试完整预览更新
        try:
            generator.update_preview(report_content)
            print("✓ 完整预览更新成功")
        except Exception as e:
            print(f"✗ 完整预览更新失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 预览显示调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_table_creation():
    """调试表格创建"""
    print("\n📊 调试表格创建...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        # 测试不同的诊断数据格式
        test_cases = [
            {
                'name': '空列表',
                'data': []
            },
            {
                'name': '单项列表',
                'data': [
                    {
                        '算法类型': '经典机器学习',
                        '算法名称': 'SVM分类器',
                        '诊断结果': '正常',
                        '置信度': '85.00%',
                        '状态评估': '正常',
                        '诊断时间': '2025-08-05 16:23:23'
                    }
                ]
            },
            {
                'name': '多项列表',
                'data': [
                    {
                        '算法类型': '经典机器学习',
                        '算法名称': 'SVM分类器',
                        '诊断结果': '正常',
                        '置信度': '85.00%',
                        '状态评估': '正常',
                        '诊断时间': '2025-08-05 16:23:23'
                    },
                    {
                        '算法类型': '深度学习',
                        '算法名称': 'CNN模型',
                        '诊断结果': '正常',
                        '置信度': '92.00%',
                        '状态评估': '正常',
                        '诊断时间': '2025-08-05 16:23:23'
                    }
                ]
            }
        ]
        
        for test_case in test_cases:
            print(f"\n测试 {test_case['name']}:")
            data = test_case['data']
            
            try:
                widget = generator.create_diagnosis_content(data)
                print(f"  ✓ 组件创建成功: {type(widget)}")
                
                if hasattr(widget, 'rowCount'):
                    print(f"  表格行数: {widget.rowCount()}")
                    print(f"  表格列数: {widget.columnCount()}")
                    print(f"  最小高度: {widget.minimumHeight()}")
                    print(f"  最大高度: {widget.maximumHeight()}")
                elif hasattr(widget, 'text'):
                    print(f"  标签文本: {widget.text()}")
                
            except Exception as e:
                print(f"  ✗ 组件创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 表格创建调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主调试函数"""
    print("🚀 开始调试诊断结果显示问题\n")
    
    # 调试诊断结果章节
    debug_diagnosis_section()
    
    # 调试预览显示
    debug_preview_display()
    
    # 调试表格创建
    debug_table_creation()
    
    print("\n📊 调试完成")
    print("请检查上述输出，找出诊断结果显示问题的根本原因")


if __name__ == "__main__":
    main()
