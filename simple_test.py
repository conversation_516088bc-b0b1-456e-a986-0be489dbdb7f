"""
简单测试报告模板
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.report_data_model import (
        ReportData, FileInfo, FeatureResult, DiagnosisResult, AnalysisSummary, 
        ReferenceFormatReportTemplate
    )
    print("✓ 报告数据模型导入成功")
except Exception as e:
    print(f"✗ 报告数据模型导入失败: {e}")
    sys.exit(1)

def test_template():
    """测试模板"""
    print("\n开始测试参考格式模板...")
    
    # 创建文件信息
    file_info = FileInfo(
        file_id=1,
        file_name="date2025.07.01--16-02-27",
        file_path="D:\\Desktop\\labview_shujucaiji6.20\\date\\",
        test_time=datetime.now(),
        vehicle_type="测试车型",
        component="变速箱",
        sensor_type="声音传感器",
        sensor_number="316F10",
        sampling_rate=48000
    )
    
    # 创建特征结果
    feature_results = [
        FeatureResult(
            feature_type="时域特征",
            feature_name="RMS",
            feature_value=2.45,
            extraction_time=datetime.now()
        )
    ]
    
    # 创建诊断结果
    diagnosis_results = [
        DiagnosisResult(
            algorithm_type="classical",
            algorithm_name="SVM分类器",
            fault_type="正常",
            confidence=0.85,
            status="normal",
            diagnosis_time=datetime.now()
        )
    ]
    
    # 创建分析摘要
    analysis_summary = AnalysisSummary(
        total_diagnoses=1,
        normal_count=1,
        warning_count=0,
        fault_count=0,
        last_diagnosis=datetime.now(),
        avg_confidence=0.85,
        total_features=1,
        feature_types=1,
        last_extraction=datetime.now()
    )
    
    # 创建报告数据
    report_data = ReportData(
        file_info=file_info,
        feature_results=feature_results,
        diagnosis_results=diagnosis_results,
        analysis_summary=analysis_summary
    )
    
    # 创建模板并生成内容
    template = ReferenceFormatReportTemplate()
    report_content = template.generate_content(report_data)
    
    print(f"✓ 报告标题: {report_content['title']}")
    print(f"✓ 报告章节数: {len(report_content['sections'])}")
    
    if report_content['sections']:
        section = report_content['sections'][0]
        print("\n✓ 第一章节内容:")
        for key, value in section['content'].items():
            print(f"   {key}: {value}")
    
    print("\n✓ 参考格式模板测试成功！")
    return True

if __name__ == "__main__":
    try:
        test_template()
        print("\n🎉 所有测试通过！")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
