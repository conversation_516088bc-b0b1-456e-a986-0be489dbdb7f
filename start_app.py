"""
启动脚本 - 解决QMimeDatabase错误
"""

import os
import sys

def setup_qt_environment():
    """设置Qt环境变量以解决QMimeDatabase错误"""
    
    # 设置Qt环境变量
    os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = ''
    os.environ['QT_QPA_PLATFORM'] = 'windows'
    
    # 禁用Qt的MIME数据库
    os.environ['QT_LOGGING_RULES'] = 'qt.qpa.mime.debug=false'
    
    # 设置Qt插件路径
    if hasattr(sys, 'frozen'):
        # 如果是打包的应用程序
        os.environ['QT_PLUGIN_PATH'] = os.path.join(sys._MEIPASS, 'PyQt5', 'Qt', 'plugins')
    
    # 禁用Qt的调试输出
    os.environ['QT_LOGGING_RULES'] = '*=false'
    
    print("✓ Qt环境变量设置完成")

def main():
    """主函数"""
    print("🚀 正在启动地面数据分析决策系统...")
    print("🔧 设置Qt环境变量以解决QMimeDatabase错误...")
    
    # 设置Qt环境
    setup_qt_environment()
    
    try:
        # 导入并运行主应用程序
        from main import main as app_main
        return app_main()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保您在正确的目录中运行此脚本")
        return 1
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
