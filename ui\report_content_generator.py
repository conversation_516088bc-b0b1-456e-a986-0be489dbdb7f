"""
报告内容生成器
负责生成报告中的图表和可视化内容
"""

try:
    import matplotlib
    matplotlib.use('Agg')  # 使用非交互式后端
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    plt = None

import numpy as np
from datetime import datetime
from typing import Dict, List, Any
import io
import base64

from ui.report_data_model import ReportData, DiagnosisResult, FeatureResult


class ReportContentGenerator:
    """报告内容生成器"""
    
    def __init__(self):
        if MATPLOTLIB_AVAILABLE:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

        # 设置图表样式
        self.colors = ['#6c5ce7', '#00cec9', '#fd79a8', '#fdcb6e', '#a29bfe']
        self.figure_size = (10, 6)
    
    def generate_diagnosis_summary_chart(self, report_data: ReportData) -> str:
        """生成诊断摘要图表"""
        if not MATPLOTLIB_AVAILABLE or not report_data.diagnosis_results:
            return ""
        
        # 统计诊断结果
        status_counts = {'normal': 0, 'warning': 0, 'fault': 0}
        for result in report_data.diagnosis_results:
            status_counts[result.status] = status_counts.get(result.status, 0) + 1
        
        # 创建饼图
        fig, ax = plt.subplots(figsize=(8, 6))
        
        labels = []
        sizes = []
        colors = []
        
        status_map = {
            'normal': ('正常', '#00b894'),
            'warning': ('警告', '#fdcb6e'),
            'fault': ('故障', '#ff7675')
        }
        
        for status, count in status_counts.items():
            if count > 0:
                label, color = status_map[status]
                labels.append(f'{label} ({count})')
                sizes.append(count)
                colors.append(color)
        
        if sizes:
            ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax.set_title('诊断结果分布', fontsize=16, fontweight='bold', pad=20)
        else:
            ax.text(0.5, 0.5, '无诊断数据', ha='center', va='center', fontsize=14)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
        
        plt.tight_layout()
        
        # 转换为base64字符串
        return self._fig_to_base64(fig)
    
    def generate_confidence_trend_chart(self, report_data: ReportData) -> str:
        """生成置信度趋势图表"""
        if not MATPLOTLIB_AVAILABLE or not report_data.diagnosis_results:
            return ""
        
        # 按时间排序
        sorted_results = sorted(report_data.diagnosis_results, key=lambda x: x.diagnosis_time)
        
        times = [result.diagnosis_time for result in sorted_results]
        confidences = [result.confidence for result in sorted_results]
        algorithms = [result.algorithm_type for result in sorted_results]
        
        fig, ax = plt.subplots(figsize=self.figure_size)
        
        # 分别绘制不同算法的置信度
        classical_times = []
        classical_confidences = []
        dl_times = []
        dl_confidences = []
        
        for time, conf, algo in zip(times, confidences, algorithms):
            if algo == 'classical':
                classical_times.append(time)
                classical_confidences.append(conf)
            else:
                dl_times.append(time)
                dl_confidences.append(conf)
        
        if classical_times:
            ax.plot(classical_times, classical_confidences, 'o-', 
                   label='经典分类器', color=self.colors[0], linewidth=2, markersize=6)
        
        if dl_times:
            ax.plot(dl_times, dl_confidences, 's-', 
                   label='深度学习', color=self.colors[1], linewidth=2, markersize=6)
        
        ax.set_xlabel('时间', fontsize=12)
        ax.set_ylabel('置信度', fontsize=12)
        ax.set_title('诊断置信度趋势', fontsize=16, fontweight='bold')
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1)
        
        # 格式化x轴时间显示
        if times:
            ax.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        return self._fig_to_base64(fig)
    
    def generate_algorithm_performance_chart(self, report_data: ReportData) -> str:
        """生成算法性能对比图表"""
        if not MATPLOTLIB_AVAILABLE or not report_data.diagnosis_results:
            return ""
        
        # 统计各算法的性能
        algo_stats = {}
        for result in report_data.diagnosis_results:
            algo_name = result.algorithm_name
            if algo_name not in algo_stats:
                algo_stats[algo_name] = {
                    'count': 0,
                    'avg_confidence': 0,
                    'fault_detected': 0
                }
            
            algo_stats[algo_name]['count'] += 1
            algo_stats[algo_name]['avg_confidence'] += result.confidence
            if result.status == 'fault':
                algo_stats[algo_name]['fault_detected'] += 1
        
        # 计算平均置信度
        for stats in algo_stats.values():
            if stats['count'] > 0:
                stats['avg_confidence'] /= stats['count']
        
        if not algo_stats:
            return ""
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 平均置信度对比
        algorithms = list(algo_stats.keys())
        avg_confidences = [algo_stats[algo]['avg_confidence'] for algo in algorithms]
        
        bars1 = ax1.bar(algorithms, avg_confidences, color=self.colors[:len(algorithms)])
        ax1.set_title('算法平均置信度', fontsize=14, fontweight='bold')
        ax1.set_ylabel('平均置信度', fontsize=12)
        ax1.set_ylim(0, 1)
        
        # 在柱状图上添加数值标签
        for bar, conf in zip(bars1, avg_confidences):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{conf:.3f}', ha='center', va='bottom', fontsize=10)
        
        # 故障检出次数对比
        fault_counts = [algo_stats[algo]['fault_detected'] for algo in algorithms]
        
        bars2 = ax2.bar(algorithms, fault_counts, color=self.colors[:len(algorithms)])
        ax2.set_title('故障检出次数', fontsize=14, fontweight='bold')
        ax2.set_ylabel('检出次数', fontsize=12)
        
        # 在柱状图上添加数值标签
        for bar, count in zip(bars2, fault_counts):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                    str(count), ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        
        return self._fig_to_base64(fig)
    
    def generate_feature_distribution_chart(self, report_data: ReportData) -> str:
        """生成特征分布图表"""
        if not MATPLOTLIB_AVAILABLE or not report_data.feature_results:
            return ""
        
        # 按特征类型分组
        feature_groups = {}
        for result in report_data.feature_results:
            feature_type = result.feature_type
            if feature_type not in feature_groups:
                feature_groups[feature_type] = []
            feature_groups[feature_type].append(result.feature_value)
        
        if not feature_groups:
            return ""
        
        # 创建子图
        num_groups = len(feature_groups)
        cols = min(3, num_groups)
        rows = (num_groups + cols - 1) // cols

        fig, axes = plt.subplots(rows, cols, figsize=(4*cols, 3*rows))

        # 确保axes始终是一个列表
        if num_groups == 1:
            axes = [axes]
        elif rows == 1 and cols == 1:
            axes = [axes]
        elif rows == 1:
            axes = list(axes) if hasattr(axes, '__iter__') else [axes]
        else:
            axes = list(axes.flatten()) if hasattr(axes, 'flatten') else [axes]
        
        for i, (feature_type, values) in enumerate(feature_groups.items()):
            # 确保我们有有效的axes对象
            if i >= len(axes):
                break

            ax = axes[i]

            # 验证ax是matplotlib axes对象
            if not hasattr(ax, 'hist') or not hasattr(ax, 'set_title'):
                print(f"警告: axes[{i}] 不是有效的matplotlib axes对象")
                continue

            # 确保values是Python列表
            if hasattr(values, 'tolist'):
                values = values.tolist()
            elif not isinstance(values, (list, tuple)):
                values = [values]

            # 确保所有值都是数字
            try:
                values = [float(v) for v in values if v is not None]
            except (ValueError, TypeError):
                values = [0.0]  # 默认值

            if not values:
                values = [0.0]  # 确保至少有一个值

            # 绘制图表
            try:
                if len(values) > 1:
                    # 如果有多个值，绘制直方图
                    ax.hist(values, bins=min(10, len(values)), alpha=0.7,
                           color=self.colors[i % len(self.colors)], edgecolor='black')
                    ax.set_xlabel('特征值', fontsize=10)
                    ax.set_ylabel('频次', fontsize=10)
                else:
                    # 如果只有一个值，绘制条形图
                    ax.bar([0], values, alpha=0.7,
                          color=self.colors[i % len(self.colors)])
                    ax.set_xticks([0])
                    ax.set_xticklabels(['值'])
                    ax.set_xlabel('样本', fontsize=10)
                    ax.set_ylabel('特征值', fontsize=10)

                ax.set_title(f'{feature_type}特征分布', fontsize=12, fontweight='bold')
                ax.grid(True, alpha=0.3)

            except Exception as e:
                # 如果绘制失败，显示错误信息
                print(f"图表绘制失败: {e}")
                try:
                    ax.text(0.5, 0.5, f'绘制失败\n{feature_type}',
                           ha='center', va='center', transform=ax.transAxes,
                           fontsize=10, color='red')
                    ax.set_title(f'{feature_type}特征分布 (错误)', fontsize=12, fontweight='bold')
                except:
                    # 如果连错误显示都失败，就跳过这个图表
                    print(f"无法显示错误信息，跳过 {feature_type} 图表")
                    continue
        
        # 隐藏多余的子图
        for i in range(num_groups, len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        return self._fig_to_base64(fig)
    
    def _fig_to_base64(self, fig) -> str:
        """将matplotlib图表转换为base64字符串"""
        if not MATPLOTLIB_AVAILABLE:
            return ""

        buffer = io.BytesIO()
        fig.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        plt.close(fig)
        return f"data:image/png;base64,{image_base64}"
    
    def generate_summary_statistics(self, report_data: ReportData) -> Dict[str, Any]:
        """生成摘要统计信息"""
        stats = {
            'total_diagnoses': len(report_data.diagnosis_results),
            'total_features': len(report_data.feature_results),
            'fault_rate': 0,
            'avg_confidence': 0,
            'feature_types': set()
        }
        
        if report_data.diagnosis_results:
            fault_count = sum(1 for result in report_data.diagnosis_results if result.status == 'fault')
            stats['fault_rate'] = fault_count / len(report_data.diagnosis_results)
            stats['avg_confidence'] = sum(result.confidence for result in report_data.diagnosis_results) / len(report_data.diagnosis_results)
        
        if report_data.feature_results:
            stats['feature_types'] = set(result.feature_type for result in report_data.feature_results)
        
        return stats
