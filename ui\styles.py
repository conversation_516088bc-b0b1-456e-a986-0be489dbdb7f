"""
样式表定义模块
包含应用程序的所有样式定义
"""

# 配色方案
PRIMARY_BG = "#bbf4ff"       # 浅蓝色背景
SECONDARY_BG = "#a8e6ff"     # 次级背景
ACCENT_COLOR = "#0066cc"     # 蓝色强调色
HIGHLIGHT_COLOR = "#0080ff"  # 蓝色高亮

# 文字颜色
TEXT_PRIMARY = "#000000"     # 主文本黑色
TEXT_SECONDARY = "#333333"   # 次级文本深灰色

# 功能色
SUCCESS_COLOR = "#00b894"    # 成功绿色
WARNING_COLOR = "#fdcb6e"    # 警告黄色
ERROR_COLOR = "#ff7675"      # 错误红色
INFO_COLOR = "#74b9ff"       # 信息蓝色

# 图表配色
CHART_COLORS = ["#6c5ce7", "#00cec9", "#fd79a8", "#fdcb6e", "#a29bfe"]


def get_main_stylesheet():
    """获取主样式表"""
    return f"""
    /* 主窗口样式 */
    QMainWindow {{
        background-color: {PRIMARY_BG};
        color: {TEXT_PRIMARY};
    }}
    
    /* 通用Widget样式 */
    QWidget {{
        background-color: {PRIMARY_BG};
        color: {TEXT_PRIMARY};
        font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
        font-size: 16px;
    }}

    /* 按钮样式 */
    QPushButton {{
        background-color: {ACCENT_COLOR};
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 20px;
        font-weight: bold;
        font-size: 16px;
        min-height: 35px;
    }}
    
    QPushButton:hover {{
        background-color: #0052a3;
    }}

    QPushButton:pressed {{
        background-color: #004080;
    }}

    QPushButton:disabled {{
        background-color: #cccccc;
        color: #666666;
    }}
    
    /* 主要按钮样式 */
    QPushButton.primary {{
        background-color: {HIGHLIGHT_COLOR};
        color: white;
    }}

    QPushButton.primary:hover {{
        background-color: #0066cc;
    }}
    
    /* 成功按钮样式 */
    QPushButton.success {{
        background-color: {SUCCESS_COLOR};
    }}
    
    QPushButton.success:hover {{
        background-color: #00c9a7;
    }}
    
    /* 危险按钮样式 */
    QPushButton.danger {{
        background-color: {ERROR_COLOR};
    }}
    
    QPushButton.danger:hover {{
        background-color: #ff8a88;
    }}
    
    /* 输入框样式 */
    QLineEdit {{
        background-color: white;
        border: 2px solid #cccccc;
        border-radius: 6px;
        padding: 12px 16px;
        font-size: 16px;
        color: {TEXT_PRIMARY};
        min-height: 30px;
    }}

    QLineEdit:focus {{
        border-color: {ACCENT_COLOR};
        background-color: #f0f8ff;
    }}

    QLineEdit:hover {{
        border-color: #999999;
    }}
    
    /* 密码输入框样式 */
    QLineEdit[echoMode="2"] {{
        lineedit-password-character: 9679;
    }}
    
    /* 标签样式 */
    QLabel {{
        color: {TEXT_PRIMARY};
        font-size: 16px;
    }}

    QLabel.title {{
        font-size: 32px;
        font-weight: bold;
        color: {ACCENT_COLOR};
        margin: 16px 0;
    }}

    QLabel.subtitle {{
        font-size: 20px;
        font-weight: 600;
        color: {TEXT_SECONDARY};
        margin: 10px 0;
    }}
    
    QLabel.error {{
        color: {ERROR_COLOR};
        font-size: 12px;
    }}
    
    QLabel.success {{
        color: {SUCCESS_COLOR};
        font-size: 12px;
    }}
    
    /* 复选框样式 */
    QCheckBox {{
        color: {TEXT_PRIMARY};
        font-size: 20px;
        spacing: 12px;
    }}

    QCheckBox::indicator {{
        width: 24px;
        height: 24px;
        border-radius: 6px;
        border: 2px solid #cccccc;
        background-color: white;
    }}

    QCheckBox::indicator:hover {{
        border-color: {ACCENT_COLOR};
    }}

    QCheckBox::indicator:checked {{
        background-color: {ACCENT_COLOR};
        border-color: {ACCENT_COLOR};
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
    }}
    
    /* 下拉框样式 */
    QComboBox {{
        background-color: white;
        border: 2px solid #cccccc;
        border-radius: 8px;
        padding: 14px 22px;
        font-size: 20px;
        color: {TEXT_PRIMARY};
        min-height: 35px;
    }}

    QComboBox:hover {{
        border-color: #999999;
    }}

    QComboBox:focus {{
        border-color: {ACCENT_COLOR};
    }}
    
    QComboBox::drop-down {{
        border: none;
        width: 30px;
    }}
    
    QComboBox::down-arrow {{
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI2I2YmVjMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
        width: 12px;
        height: 8px;
    }}
    
    QComboBox QAbstractItemView {{
        background-color: white;
        border: 2px solid #cccccc;
        border-radius: 8px;
        selection-background-color: {ACCENT_COLOR};
        color: {TEXT_PRIMARY};
        padding: 8px;
        font-size: 20px;
    }}
    
    /* 列表样式 */
    QListWidget {{
        background-color: white;
        border: 2px solid #cccccc;
        border-radius: 8px;
        color: {TEXT_PRIMARY};
        font-size: 20px;
        outline: none;
    }}

    QListWidget::item {{
        padding: 16px;
        border-bottom: 1px solid #eeeeee;
    }}

    QListWidget::item:hover {{
        background-color: #f0f8ff;
    }}

    QListWidget::item:selected {{
        background-color: {ACCENT_COLOR};
        color: white;
    }}
    
    /* 表格样式 */
    QTableWidget {{
        background-color: white;
        border: 2px solid #cccccc;
        border-radius: 8px;
        color: {TEXT_PRIMARY};
        gridline-color: #eeeeee;
        font-size: 20px;
        font-family: 'Microsoft YaHei';
        alternate-background-color: #f8f9fa;
    }}

    QTableWidget::item {{
        padding: 18px;
        border-bottom: 1px solid #eeeeee;
        background-color: white;
    }}

    QTableWidget::item:hover {{
        background-color: #f0f8ff;
    }}

    QTableWidget::item:selected {{
        background-color: {ACCENT_COLOR};
        color: white;
    }}

    QHeaderView::section {{
        background-color: {SECONDARY_BG};
        color: {TEXT_PRIMARY};
        padding: 18px;
        border: 1px solid #cccccc;
        font-weight: bold;
        font-size: 20px;
        font-family: 'Microsoft YaHei';
    }}
    
    /* 滚动条样式 */
    QScrollBar:vertical {{
        background-color: #f0f0f0;
        width: 16px;
        border-radius: 8px;
        margin: 0;
    }}

    QScrollBar::handle:vertical {{
        background-color: #cccccc;
        border-radius: 8px;
        min-height: 24px;
        margin: 2px;
    }}

    QScrollBar::handle:vertical:hover {{
        background-color: {ACCENT_COLOR};
    }}
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    
    QScrollBar:horizontal {{
        background-color: #f0f0f0;
        height: 16px;
        border-radius: 8px;
        margin: 0;
    }}

    QScrollBar::handle:horizontal {{
        background-color: #cccccc;
        border-radius: 8px;
        min-width: 24px;
        margin: 2px;
    }}

    QScrollBar::handle:horizontal:hover {{
        background-color: {ACCENT_COLOR};
    }}
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {{
        width: 0px;
    }}
    
    /* 分组框样式 */
    QGroupBox {{
        color: {TEXT_PRIMARY};
        border: 2px solid #cccccc;
        border-radius: 8px;
        margin-top: 15px;
        font-weight: bold;
        font-size: 22px;
    }}

    QGroupBox::title {{
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 15px 0 15px;
        color: {ACCENT_COLOR};
    }}
    
    /* 状态栏样式 */
    QStatusBar {{
        background-color: {PRIMARY_BG};
        color: {TEXT_SECONDARY};
        border-top: 1px solid #3a3a4a;
    }}
    
    /* 菜单栏样式 */
    QMenuBar {{
        background-color: {PRIMARY_BG};
        color: {TEXT_PRIMARY};
        border-bottom: 1px solid #cccccc;
        font-size: 14px;
        padding: 4px;
        height: 30px;
        font-family: 'Microsoft YaHei';
    }}

    QMenuBar::item {{
        padding: 6px 12px;
        background-color: transparent;
        border-radius: 3px;
        margin: 2px;
    }}

    QMenuBar::item:hover {{
        background-color: {ACCENT_COLOR};
        color: white;
    }}
    
    QMenu {{
        background-color: {SECONDARY_BG};
        color: {TEXT_PRIMARY};
        border: 1px solid #3a3a4a;
        border-radius: 8px;
    }}
    
    QMenu::item {{
        padding: 8px 20px;
    }}
    
    QMenu::item:hover {{
        background-color: {ACCENT_COLOR};
    }}
    """


def get_login_stylesheet():
    """获取登录界面专用样式表"""
    return get_main_stylesheet() + f"""
    /* 登录界面特殊样式 */
    .login-container {{
        background-color: {SECONDARY_BG};
        border-radius: 15px;
        border: 1px solid #3a3a4a;
    }}
    
    .login-title {{
        font-size: 28px;
        font-weight: bold;
        color: {ACCENT_COLOR};
        margin: 20px 0;
    }}
    
    .login-subtitle {{
        font-size: 14px;
        color: {TEXT_SECONDARY};
        margin-bottom: 30px;
    }}
    
    .login-input {{
        min-height: 25px;
        font-size: 14px;
        margin: 5px 0;
    }}
    
    .login-button {{
        min-height: 35px;
        font-size: 14px;
        font-weight: bold;
        margin: 10px 0;
    }}
    """
