"""
全面调试诊断结果显示问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_report_generation_workflow():
    """调试完整的报告生成工作流程"""
    print("🔍 调试完整报告生成工作流程...")
    
    try:
        from ui.report_generator import ReportGenerationThread
        from database.db_manager import DatabaseManager
        
        # 创建数据库管理器
        db = DatabaseManager()
        
        # 模拟报告配置
        report_config = {
            'file_ids': [1],  # 假设使用第一个文件
            'report_type': 'single',
            'template_type': 'reference'
        }
        
        # 创建报告生成线程
        thread = ReportGenerationThread(db, report_config)
        
        print("✓ 报告生成线程创建成功")
        
        # 手动执行报告生成逻辑
        try:
            # 获取文件信息
            file_ids = report_config.get('file_ids', [])
            if not file_ids:
                print("✗ 没有选择文件ID")
                return False
            
            print(f"📁 处理文件ID: {file_ids}")
            
            # 获取文件信息
            file_info_list = []
            for file_id in file_ids:
                file_info_data = db.get_file_info(file_id)
                if file_info_data:
                    print(f"✓ 获取文件信息成功: {file_info_data.get('TDMS文件名', 'Unknown')}")
                    file_info_list.append(file_info_data)
                else:
                    print(f"✗ 获取文件信息失败: ID {file_id}")
            
            if not file_info_list:
                print("✗ 没有有效的文件信息")
                return False
            
            # 创建报告数据
            report_data_list = []
            for file_info_data in file_info_list:
                file_id = file_info_data['ID']
                
                # 创建FileInfo对象
                from ui.report_data_model import FileInfo
                file_info = FileInfo(
                    file_id=file_info_data['ID'],
                    file_name=file_info_data['TDMS文件名'],
                    file_path=file_info_data['TDMS文件路径'],
                    test_time=thread.safe_parse_datetime(file_info_data['测试时间']),
                    vehicle_type=file_info_data['车型'],
                    component=file_info_data['部件'],
                    sensor_type=file_info_data['传感器类型'],
                    sensor_number=file_info_data['传感器编号'],
                    sampling_rate=48000
                )
                
                print(f"✓ FileInfo对象创建成功")
                
                # 获取诊断结果
                diagnosis_data = db.get_diagnosis_results(file_id)
                print(f"📊 诊断结果数据: {len(diagnosis_data) if diagnosis_data else 0} 条记录")
                
                if diagnosis_data:
                    print("诊断结果详情:")
                    for i, diag in enumerate(diagnosis_data[:3]):  # 只显示前3条
                        print(f"  {i+1}. 算法: {diag.get('algorithm_name', 'N/A')}")
                        print(f"     类型: {diag.get('algorithm_type', 'N/A')}")
                        print(f"     结果: {diag.get('fault_type', 'N/A')}")
                        print(f"     置信度: {diag.get('confidence', 'N/A')}")
                
                # 获取特征结果
                feature_data = db.get_feature_extraction_results(file_id)
                print(f"📈 特征结果数据: {len(feature_data) if feature_data else 0} 条记录")
                
                # 创建模拟数据（如果数据库中没有数据）
                if not diagnosis_data:
                    print("⚠️ 数据库中没有诊断结果，创建模拟数据")
                    from ui.report_data_model import DiagnosisResult
                    diagnosis_results = [
                        DiagnosisResult("classical", "SVM分类器", "正常", 0.85, "normal", datetime.now()),
                        DiagnosisResult("deep_learning", "CNN模型", "正常", 0.92, "normal", datetime.now()),
                        DiagnosisResult("classical", "随机森林", "正常", 0.88, "normal", datetime.now())
                    ]
                else:
                    from ui.report_data_model import DiagnosisResult
                    diagnosis_results = []
                    for diag in diagnosis_data:
                        diagnosis_result = DiagnosisResult(
                            algorithm_type=diag['algorithm_type'],
                            algorithm_name=diag['algorithm_name'],
                            fault_type=diag['fault_type'],
                            confidence=float(diag['confidence']) if diag['confidence'] else 0.0,
                            status=diag['status'],
                            diagnosis_time=thread.safe_parse_datetime(diag['diagnosis_time']),
                            details=None
                        )
                        diagnosis_results.append(diagnosis_result)
                
                if not feature_data:
                    print("⚠️ 数据库中没有特征结果，创建模拟数据")
                    from ui.report_data_model import FeatureResult
                    feature_results = [
                        FeatureResult("时域特征", "RMS", 2.45, datetime.now()),
                        FeatureResult("频域特征", "主频", 1200.5, datetime.now())
                    ]
                else:
                    from ui.report_data_model import FeatureResult
                    feature_results = []
                    for feat in feature_data:
                        feature_result = FeatureResult(
                            feature_type=feat['feature_type'],
                            feature_name=feat['feature_name'],
                            feature_value=float(feat['feature_value']) if feat['feature_value'] else 0.0,
                            extraction_time=thread.safe_parse_datetime(feat['extraction_time']),
                            parameters=None
                        )
                        feature_results.append(feature_result)
                
                # 创建分析摘要
                from ui.report_data_model import AnalysisSummary
                analysis_summary = AnalysisSummary(
                    total_diagnoses=len(diagnosis_results),
                    normal_count=len([d for d in diagnosis_results if d.status == 'normal']),
                    warning_count=len([d for d in diagnosis_results if d.status == 'warning']),
                    fault_count=len([d for d in diagnosis_results if d.status == 'fault']),
                    last_diagnosis=diagnosis_results[-1].diagnosis_time if diagnosis_results else datetime.now(),
                    avg_confidence=sum(d.confidence for d in diagnosis_results) / len(diagnosis_results) if diagnosis_results else 0.0,
                    total_features=len(feature_results),
                    feature_types=len(set(f.feature_type for f in feature_results)),
                    last_extraction=feature_results[-1].extraction_time if feature_results else datetime.now()
                )
                
                # 创建报告数据
                from ui.report_data_model import ReportData
                report_data = ReportData(
                    file_info=file_info,
                    feature_results=feature_results,
                    diagnosis_results=diagnosis_results,
                    analysis_summary=analysis_summary
                )
                
                report_data_list.append(report_data)
                print(f"✓ 报告数据创建成功")
            
            # 生成报告内容
            if report_data_list:
                from ui.report_data_model import ReferenceFormatReportTemplate
                template = ReferenceFormatReportTemplate()
                report_content = template.generate_content(report_data_list[0])
                
                print(f"✓ 报告内容生成成功")
                print(f"  标题: {report_content['title']}")
                print(f"  章节数: {len(report_content['sections'])}")
                
                # 详细检查每个章节
                for i, section in enumerate(report_content['sections']):
                    title = section.get('title', f'章节{i+1}')
                    section_type = section.get('type', '未知')
                    content = section.get('content', {})
                    
                    print(f"\n📋 章节 {i+1}: {title}")
                    print(f"  类型: {section_type}")
                    print(f"  内容类型: {type(content)}")
                    
                    if isinstance(content, list):
                        print(f"  列表长度: {len(content)}")
                        if content:
                            print(f"  第一项类型: {type(content[0])}")
                            if isinstance(content[0], dict):
                                print(f"  第一项键: {list(content[0].keys())}")
                    elif isinstance(content, dict):
                        print(f"  字典长度: {len(content)}")
                        print(f"  字典键: {list(content.keys())[:5]}...")  # 只显示前5个键
                
                return report_content
            
        except Exception as e:
            print(f"✗ 报告生成过程失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"✗ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def debug_preview_rendering(report_content):
    """调试预览渲染"""
    print("\n🖥️ 调试预览渲染...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        print("✓ 报告生成器创建成功")
        
        # 找到诊断结果章节
        diagnosis_section = None
        features_section = None
        
        for section in report_content['sections']:
            title = section.get('title', '')
            if '诊断结果' in title:
                diagnosis_section = section
            elif '特征分析' in title:
                features_section = section
        
        # 测试诊断结果章节
        if diagnosis_section:
            print(f"\n📊 测试诊断结果章节:")
            print(f"  标题: {diagnosis_section['title']}")
            print(f"  类型: {diagnosis_section['type']}")
            content = diagnosis_section['content']
            print(f"  内容类型: {type(content)}")
            print(f"  内容长度: {len(content) if hasattr(content, '__len__') else 'N/A'}")
            
            if isinstance(content, list) and content:
                print(f"  第一项内容: {content[0]}")
            
            try:
                diagnosis_widget = generator.create_diagnosis_content(content)
                print(f"✓ 诊断内容组件创建成功: {type(diagnosis_widget)}")
                
                if hasattr(diagnosis_widget, 'rowCount'):
                    print(f"  表格行数: {diagnosis_widget.rowCount()}")
                    print(f"  表格列数: {diagnosis_widget.columnCount()}")
                    print(f"  表格高度: {diagnosis_widget.minimumHeight()}px")
                    print(f"  表格宽度: {diagnosis_widget.minimumWidth()}px")
                    
                    # 检查表格内容
                    print(f"  表格内容检查:")
                    for row in range(min(2, diagnosis_widget.rowCount())):
                        row_data = []
                        for col in range(diagnosis_widget.columnCount()):
                            item = diagnosis_widget.item(row, col)
                            if item:
                                text = item.text()
                                row_data.append(text[:10] + "..." if len(text) > 10 else text)
                            else:
                                row_data.append("空")
                        print(f"    行 {row+1}: {row_data}")
                
            except Exception as e:
                print(f"✗ 诊断内容组件创建失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试特征分析章节
        if features_section:
            print(f"\n📈 测试特征分析章节:")
            print(f"  标题: {features_section['title']}")
            print(f"  类型: {features_section['type']}")
            content = features_section['content']
            print(f"  内容类型: {type(content)}")
            print(f"  内容长度: {len(content) if hasattr(content, '__len__') else 'N/A'}")
            
            try:
                features_widget = generator.create_features_content(content)
                print(f"✓ 特征内容组件创建成功: {type(features_widget)}")
                
                if hasattr(features_widget, 'rowCount'):
                    print(f"  表格行数: {features_widget.rowCount()}")
                    print(f"  表格列数: {features_widget.columnCount()}")
                    print(f"  表格高度: {features_widget.minimumHeight()}px")
                
            except Exception as e:
                print(f"✗ 特征内容组件创建失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 测试完整预览更新
        try:
            print(f"\n🔄 测试完整预览更新...")
            generator.update_preview(report_content)
            print("✓ 完整预览更新成功")
            
            # 检查预览区域
            preview_widget = generator.preview_area.widget()
            if preview_widget:
                print(f"✓ 预览组件存在: {type(preview_widget)}")
                print(f"  预览组件大小: {preview_widget.size()}")
                print(f"  预览组件最小大小: {preview_widget.minimumSize()}")
            else:
                print("✗ 预览组件不存在")
            
        except Exception as e:
            print(f"✗ 完整预览更新失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"✗ 预览渲染调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主调试函数"""
    print("🚀 开始全面调试诊断结果显示问题\n")
    
    # 1. 调试报告生成工作流程
    report_content = debug_report_generation_workflow()
    
    if report_content:
        # 2. 调试预览渲染
        debug_preview_rendering(report_content)
    else:
        print("✗ 报告生成失败，无法继续调试预览渲染")
    
    print("\n📊 调试完成")
    print("请检查上述输出，找出显示问题的根本原因")


if __name__ == "__main__":
    main()
