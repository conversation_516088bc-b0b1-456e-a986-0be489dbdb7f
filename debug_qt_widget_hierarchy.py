"""
专门调试Qt组件层次结构和诊断结果渲染问题
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_qt_widget_hierarchy():
    """调试Qt组件层次结构"""
    print("🔍 调试Qt组件层次结构和诊断结果渲染...")
    
    try:
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from PyQt5.QtCore import QEventLoop, QTimer
        from ui.report_generator import ReportGenerator, ReportGenerationThread
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建数据库管理器和报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        print(f"✓ 报告生成器创建成功")
        print(f"  预览区域类型: {type(generator.preview_area)}")
        print(f"  预览区域大小: {generator.preview_area.size()}")
        print(f"  预览区域可见性: {generator.preview_area.isVisible()}")
        
        # 检查初始预览状态
        initial_widget = generator.preview_area.widget()
        print(f"📋 初始预览组件: {type(initial_widget)}")
        if hasattr(initial_widget, 'text'):
            print(f"  初始文本: {initial_widget.text()}")
        
        # 模拟实际的报告生成流程
        print("\n🚀 开始模拟报告生成流程...")
        
        # 创建报告配置
        report_config = {
            'file_ids': [1],
            'report_type': 'single',
            'template_type': 'reference'
        }
        
        # 创建报告生成线程
        thread = ReportGenerationThread(db, report_config)
        
        # 设置信号处理
        result_data = {'success': False, 'report_content': None}
        
        def on_report_generated(result):
            print(f"✓ 报告生成完成，开始更新预览...")
            result_data['success'] = True
            result_data['report_content'] = result['report_content']
            
            # 调用实际的预览更新方法
            generator.update_preview(result['report_content'])
            print(f"✓ 预览更新完成")
        
        def on_error_occurred(error):
            print(f"✗ 报告生成错误: {error}")
        
        # 连接信号
        thread.report_generated.connect(on_report_generated)
        thread.error_occurred.connect(on_error_occurred)
        
        # 启动线程并等待完成
        thread.start()
        
        # 创建事件循环等待完成
        loop = QEventLoop()
        thread.finished.connect(loop.quit)
        timer = QTimer()
        timer.timeout.connect(loop.quit)
        timer.start(10000)  # 10秒超时
        loop.exec_()
        timer.stop()
        
        if not result_data['success']:
            print("✗ 报告生成失败")
            return False
        
        # 详细检查预览组件层次结构
        print("\n🔍 详细检查预览组件层次结构...")
        
        # 1. 检查预览区域
        preview_area = generator.preview_area
        print(f"📋 预览区域 (QScrollArea):")
        print(f"  类型: {type(preview_area)}")
        print(f"  大小: {preview_area.size()}")
        print(f"  可见性: {preview_area.isVisible()}")
        print(f"  启用状态: {preview_area.isEnabled()}")
        print(f"  几何: {preview_area.geometry()}")
        
        # 2. 检查预览组件
        preview_widget = preview_area.widget()
        print(f"\n📋 预览组件 (QWidget):")
        print(f"  类型: {type(preview_widget)}")
        print(f"  大小: {preview_widget.size()}")
        print(f"  可见性: {preview_widget.isVisible()}")
        print(f"  启用状态: {preview_widget.isEnabled()}")
        print(f"  几何: {preview_widget.geometry()}")
        
        # 3. 检查预览组件的布局
        layout = preview_widget.layout()
        if layout:
            print(f"\n📋 预览布局:")
            print(f"  布局类型: {type(layout)}")
            print(f"  布局项目数: {layout.count()}")
            print(f"  布局边距: {layout.contentsMargins()}")
            print(f"  布局间距: {layout.spacing()}")
        
        # 4. 检查章节组件
        from PyQt5.QtWidgets import QFrame, QTableWidget
        frames = preview_widget.findChildren(QFrame)
        print(f"\n📋 找到章节框架数量: {len(frames)}")
        
        diagnosis_frame = None
        for i, frame in enumerate(frames):
            print(f"  框架 {i+1}:")
            print(f"    类型: {type(frame)}")
            print(f"    大小: {frame.size()}")
            print(f"    可见性: {frame.isVisible()}")
            print(f"    几何: {frame.geometry()}")
            
            # 查找诊断结果框架
            frame_layout = frame.layout()
            if frame_layout and frame_layout.count() > 0:
                title_widget = frame_layout.itemAt(0).widget()
                if hasattr(title_widget, 'text') and '诊断结果' in title_widget.text():
                    diagnosis_frame = frame
                    print(f"    *** 这是诊断结果框架 ***")
        
        # 5. 详细检查诊断结果框架
        if diagnosis_frame:
            print(f"\n🎯 详细检查诊断结果框架:")
            print(f"  框架大小: {diagnosis_frame.size()}")
            print(f"  框架可见性: {diagnosis_frame.isVisible()}")
            print(f"  框架几何: {diagnosis_frame.geometry()}")
            
            # 检查框架布局
            frame_layout = diagnosis_frame.layout()
            if frame_layout:
                print(f"  布局项目数: {frame_layout.count()}")
                for j in range(frame_layout.count()):
                    item = frame_layout.itemAt(j)
                    if item:
                        widget = item.widget()
                        if widget:
                            print(f"    项目 {j}: {type(widget)}")
                            print(f"      大小: {widget.size()}")
                            print(f"      可见性: {widget.isVisible()}")
                            print(f"      几何: {widget.geometry()}")
                            
                            # 如果是表格，详细检查
                            if isinstance(widget, QTableWidget):
                                print(f"      *** 找到诊断结果表格 ***")
                                print(f"      行数: {widget.rowCount()}")
                                print(f"      列数: {widget.columnCount()}")
                                print(f"      最小高度: {widget.minimumHeight()}")
                                print(f"      最大高度: {widget.maximumHeight()}")
                                
                                # 检查表格内容
                                if widget.rowCount() > 0 and widget.columnCount() > 0:
                                    print(f"      表格内容检查:")
                                    for row in range(min(2, widget.rowCount())):
                                        row_data = []
                                        for col in range(widget.columnCount()):
                                            item = widget.item(row, col)
                                            if item:
                                                text = item.text()
                                                row_data.append(text[:10] + "..." if len(text) > 10 else text)
                                            else:
                                                row_data.append("空")
                                        print(f"        行 {row+1}: {row_data}")
                                
                                # 检查表格样式
                                style_sheet = widget.styleSheet()
                                if style_sheet:
                                    print(f"      样式表长度: {len(style_sheet)}")
                                    print(f"      样式表前100字符: {style_sheet[:100]}...")
        else:
            print(f"\n❌ 未找到诊断结果框架")
        
        # 6. 检查所有表格组件
        tables = preview_widget.findChildren(QTableWidget)
        print(f"\n📊 找到表格总数: {len(tables)}")
        
        for i, table in enumerate(tables):
            print(f"  表格 {i+1}:")
            print(f"    大小: {table.size()}")
            print(f"    可见性: {table.isVisible()}")
            print(f"    行数: {table.rowCount()}")
            print(f"    列数: {table.columnCount()}")
            print(f"    几何: {table.geometry()}")
            print(f"    父组件: {type(table.parent())}")
            
            # 检查第一个单元格内容
            if table.rowCount() > 0 and table.columnCount() > 0:
                item = table.item(0, 0)
                if item:
                    text = item.text()
                    print(f"    第一个单元格: '{text}'")
                    
                    # 判断是否是诊断结果表格
                    if any(keyword in text for keyword in ['经典机器学习', '深度学习', '算法类型']):
                        print(f"    *** 这可能是诊断结果表格 ***")
        
        return True
        
    except Exception as e:
        print(f"✗ Qt组件层次结构调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主调试函数"""
    print("🚀 开始Qt组件层次结构调试\n")
    
    success = debug_qt_widget_hierarchy()
    
    if success:
        print("\n✅ Qt组件层次结构调试完成")
        print("\n💡 请检查上述输出，特别关注:")
        print("   1. 诊断结果框架是否被找到")
        print("   2. 诊断结果表格是否存在且可见")
        print("   3. 表格内容是否正确")
        print("   4. 组件几何和可见性设置")
    else:
        print("\n❌ Qt组件层次结构调试失败")


if __name__ == "__main__":
    main()
