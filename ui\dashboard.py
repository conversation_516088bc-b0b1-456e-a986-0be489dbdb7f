"""
仪表盘界面模块
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QFrame, QGridLayout, QPushButton, QProgressBar)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPainter, QColor, QBrush
from ui.styles import ACCENT_COLOR, TEXT_PRIMARY, TEXT_SECONDARY, SUCCESS_COLOR, WARNING_COLOR, ERROR_COLOR, PRIMARY_BG
import datetime


class StatCard(QFrame):
    """统计卡片组件"""
    
    def __init__(self, title, value, icon, color=ACCENT_COLOR):
        super().__init__()
        self.title = title
        self.value = value
        self.icon = icon
        self.color = color
        self.init_ui()
    
    def init_ui(self):
        """初始化卡片界面 - 修复文字显示被遮挡问题"""
        self.setFixedHeight(160)  # 增加高度确保文字完整显示
        self.setFixedWidth(250)   # 固定宽度以适应1x4布局
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 8px;
                border: 1px solid #cccccc;
                padding: 10px;
            }}
            QFrame:hover {{
                border-color: {self.color};
                background-color: #f0f8ff;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(6)

        # 顶部布局（图标和标题）
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 3)

        # 图标
        self.icon_label = QLabel(self.icon)
        self.icon_label.setStyleSheet(f"""
            font-size: 22px;
            color: {self.color};
            font-weight: bold;
            background-color: transparent;
        """)
        top_layout.addWidget(self.icon_label)

        top_layout.addStretch()

        # 标题
        self.title_label = QLabel(self.title)
        self.title_label.setStyleSheet(f"""
            font-size: 16px;
            color: {TEXT_PRIMARY};
            font-weight: bold;
            padding-right: 6px;
            background-color: transparent;
            font-family: 'Microsoft YaHei';
        """)
        top_layout.addWidget(self.title_label)

        layout.addLayout(top_layout)

        # 数值容器 - 增加高度确保文字完整显示
        value_container = QFrame()
        value_container.setFixedHeight(85)  # 增加高度确保32px字体完整显示
        value_container.setStyleSheet(f"""
            background-color: {PRIMARY_BG};
            border-radius: 6px;
            border: 1px solid #cccccc;
        """)
        value_layout = QVBoxLayout(value_container)
        value_layout.setContentsMargins(6, 6, 6, 6)
        value_layout.setAlignment(Qt.AlignCenter)

        # 数值
        self.value_label = QLabel(str(self.value))
        self.value_label.setAlignment(Qt.AlignCenter)
        self.value_label.setStyleSheet(f"""
            font-size: 32px;
            color: {TEXT_PRIMARY};
            font-weight: bold;
            padding: 2px;
            background-color: transparent;
            font-family: 'Microsoft YaHei';
        """)

        value_layout.addWidget(self.value_label)
        layout.addWidget(value_container)
    
    def update_value(self, new_value):
        """更新卡片数值"""
        self.value = new_value
        self.value_label.setText(str(new_value))  # 直接更新标签文本


class SystemStatus(QFrame):
    """系统状态组件"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化状态界面"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 10px;
                border: 2px solid #cccccc;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)  # 进一步紧凑的内部边距
        layout.setSpacing(10)  # 进一步紧凑的间距

        # 标题
        title_label = QLabel("系统状态")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            color: {ACCENT_COLOR};
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
            background-color: transparent;
        """)
        layout.addWidget(title_label)

        # 状态项目容器
        status_container = QFrame()
        status_container.setStyleSheet(f"""
            background-color: {PRIMARY_BG};
            border-radius: 6px;
            border: 1px solid #cccccc;
            padding: 6px;
        """)
        status_layout = QVBoxLayout(status_container)
        status_layout.setSpacing(10)  # 进一步紧凑的项目间距
        status_layout.setContentsMargins(10, 10, 10, 10)  # 进一步紧凑的内边距
        
        # 状态项目
        status_items = [
            ("数据库连接", "正常", SUCCESS_COLOR),
            ("文件系统", "正常", SUCCESS_COLOR),
            ("算法引擎", "就绪", SUCCESS_COLOR),
            ("内存使用", "良好", SUCCESS_COLOR),
        ]
        
        for name, status, color in status_items:
            item_layout = QHBoxLayout()
            item_layout.setContentsMargins(8, 6, 8, 6)  # 紧凑的项目内边距

            # 状态指示点
            indicator = QLabel("●")
            indicator.setStyleSheet(f"""
                color: {color};
                font-size: 16px;
                background-color: transparent;
                padding-right: 6px;
            """)
            item_layout.addWidget(indicator)

            # 状态名称
            name_label = QLabel(name)
            name_label.setStyleSheet(f"""
                color: {TEXT_PRIMARY};
                font-size: 16px;
                font-family: 'Microsoft YaHei';
                background-color: transparent;
                padding: 0 8px;
            """)
            item_layout.addWidget(name_label)

            item_layout.addStretch()

            # 状态值
            status_label = QLabel(status)
            status_label.setStyleSheet(f"""
                color: {color};
                font-size: 16px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                background-color: transparent;
                padding-left: 8px;
            """)
            item_layout.addWidget(status_label)
            
            status_layout.addLayout(item_layout)
        
        layout.addWidget(status_container)


class QuickActions(QFrame):
    """快速操作组件"""
    action_clicked = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化快速操作界面"""
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border-radius: 10px;
                border: 2px solid #cccccc;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(12, 12, 12, 12)  # 进一步紧凑的内部边距
        layout.setSpacing(10)  # 进一步紧凑的间距

        # 标题
        title_label = QLabel("快速操作")
        title_label.setStyleSheet(f"""
            font-size: 20px;
            color: {ACCENT_COLOR};
            font-weight: bold;
            margin-bottom: 10px;
            font-family: 'Microsoft YaHei';
            background-color: transparent;
        """)
        layout.addWidget(title_label)

        # 操作按钮容器
        buttons_container = QFrame()
        buttons_container.setStyleSheet(f"""
            background-color: {PRIMARY_BG};
            border-radius: 6px;
            border: 1px solid #cccccc;
            padding: 6px;
        """)
        buttons_layout = QVBoxLayout(buttons_container)
        buttons_layout.setSpacing(8)  # 进一步紧凑的按钮间距
        buttons_layout.setContentsMargins(10, 10, 10, 10)  # 进一步紧凑的内边距
        
        # 操作按钮
        actions = [
            ("📁", "选择文件", "file_select"),
            ("📊", "特征分析", "feature_analysis"),
            ("🔍", "故障检测", "fault_detection"),
            ("📋", "生成报告", "generate_report"),
        ]
        
        for icon, text, action in actions:
            button = QPushButton(f"{icon}  {text}")
            button.setStyleSheet(f"""
                QPushButton {{
                    text-align: left;
                    padding: 10px 14px;  /* 进一步紧凑的按钮内边距 */
                    margin: 2px 0;
                    border: 1px solid #cccccc;
                    border-radius: 5px;
                    background-color: white;
                    color: {TEXT_PRIMARY};
                    font-size: 15px;  /* 稍小的字体大小 */
                    font-family: 'Microsoft YaHei';
                    min-height: 32px;  /* 更紧凑的按钮高度 */
                }}
                QPushButton:hover {{
                    background-color: {ACCENT_COLOR};
                    color: white;
                    border: none;
                }}
            """)
            button.clicked.connect(lambda checked, a=action: self.action_clicked.emit(a))
            buttons_layout.addWidget(button)
        
        layout.addWidget(buttons_container)


class Dashboard(QWidget):
    """仪表盘主界面"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.init_ui()
        self.setup_timer()
        self.load_statistics()
    
    def init_ui(self):
        """初始化仪表盘界面 - 修复文字显示问题后的布局调整"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)  # 紧凑的外边距
        layout.setSpacing(12)  # 进一步压缩间距以适应更高的卡片

        # 标题和时间 - 压缩高度
        header_layout = QHBoxLayout()

        title_label = QLabel("系统仪表盘")
        title_label.setStyleSheet(f"""
            font-size: 30px;
            font-weight: bold;
            color: {ACCENT_COLOR};
            font-family: 'Microsoft YaHei';
            background-color: transparent;
            padding: 6px 0;
        """)
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        self.time_label = QLabel()
        self.time_label.setStyleSheet(f"""
            font-size: 15px;
            color: {TEXT_SECONDARY};
            font-family: 'Microsoft YaHei';
            background-color: transparent;
            padding: 6px;
        """)
        header_layout.addWidget(self.time_label)

        layout.addLayout(header_layout)

        # 统计卡片区域 - 改回1x4横向布局
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(12)  # 稍微减少卡片间距
        stats_layout.setContentsMargins(0, 6, 0, 6)

        # 创建统计卡片
        self.total_files_card = StatCard("总文件数", "0", "📁", ACCENT_COLOR)
        self.processed_files_card = StatCard("已处理", "0", "✅", SUCCESS_COLOR)
        self.alerts_card = StatCard("报警数量", "0", "⚠️", WARNING_COLOR)
        self.accuracy_card = StatCard("检测精度", "0%", "🎯", SUCCESS_COLOR)

        # 1x4横向布局
        stats_layout.addWidget(self.total_files_card)
        stats_layout.addWidget(self.processed_files_card)
        stats_layout.addWidget(self.alerts_card)
        stats_layout.addWidget(self.accuracy_card)

        layout.addLayout(stats_layout)
        
        # 下方内容区域
        content_layout = QHBoxLayout()
        content_layout.setSpacing(16)  # 稍微减少间距

        # 左侧：系统状态
        self.system_status = SystemStatus()
        content_layout.addWidget(self.system_status, 1)

        # 右侧：快速操作
        self.quick_actions = QuickActions()
        self.quick_actions.action_clicked.connect(self.on_quick_action)
        content_layout.addWidget(self.quick_actions, 1)

        layout.addLayout(content_layout)

        # 底部状态信息
        self.status_info = QLabel("系统运行正常")
        self.status_info.setStyleSheet(f"""
            color: {TEXT_PRIMARY};
            font-size: 13px;
            padding: 10px;
            background-color: white;
            border-radius: 6px;
            border: 1px solid #cccccc;
            font-family: 'Microsoft YaHei';
        """)
        layout.addWidget(self.status_info)
    
    def setup_timer(self):
        """设置定时器"""
        # 时间更新定时器
        self.time_timer = QTimer()
        self.time_timer.timeout.connect(self.update_time)
        self.time_timer.start(1000)  # 每秒更新
        self.update_time()
        
        # 统计数据更新定时器
        self.stats_timer = QTimer()
        self.stats_timer.timeout.connect(self.load_statistics)
        self.stats_timer.start(30000)  # 每30秒更新统计数据
    
    def update_time(self):
        """更新时间显示"""
        current_time = datetime.datetime.now()
        time_str = current_time.strftime("%Y年%m月%d日 %H:%M:%S")
        self.time_label.setText(time_str)
    
    def load_statistics(self):
        """加载统计数据"""
        try:
            # 获取文件总数
            files = self.db_manager.get_tdms_files()
            total_files = len(files)
            
            # 更新统计卡片
            self.total_files_card.update_value(total_files)
            self.processed_files_card.update_value(total_files // 2)  # 示例数据
            self.alerts_card.update_value(3)  # 示例数据
            self.accuracy_card.update_value("95.2%")  # 示例数据
            
            # 更新状态信息
            self.status_info.setText(f"最后更新: {datetime.datetime.now().strftime('%H:%M:%S')} | "
                                   f"数据库连接正常 | 共{total_files}个文件")
            
        except Exception as e:
            self.status_info.setText(f"数据加载失败: {str(e)}")
            self.status_info.setStyleSheet(f"""
                color: {ERROR_COLOR};
                font-size: 13px;
                padding: 10px;
                background-color: white;
                border-radius: 6px;
                border: 1px solid #cccccc;
                font-family: 'Microsoft YaHei';
            """)
    
    def on_quick_action(self, action):
        """处理快速操作"""
        # 通过信号发送页面切换请求
        try:
            # 寻找主窗口
            main_window = self
            while main_window.parent():
                main_window = main_window.parent()
                if hasattr(main_window, 'navigation'):
                    break

            if hasattr(main_window, 'navigation'):
                if action == "file_select":
                    # 切换到文件选择页面
                    main_window.navigation.set_current_page(1)
                elif action == "feature_analysis":
                    # 切换到特征分析页面
                    main_window.navigation.set_current_page(2)
                elif action == "fault_detection":
                    # 切换到故障检测页面
                    main_window.navigation.set_current_page(3)
                elif action == "generate_report":
                    # 切换到报告生成页面
                    main_window.navigation.set_current_page(6)
        except Exception as e:
            print(f"快速操作错误: {e}")
