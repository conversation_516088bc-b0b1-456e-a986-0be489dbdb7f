"""
调试表格显示问题
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_table():
    """测试简单表格显示"""
    print("🔍 测试简单表格显示...")
    
    try:
        # 设置Qt环境变量
        os.environ['QT_QPA_PLATFORM'] = 'windows'
        os.environ['QT_LOGGING_RULES'] = '*=false'
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTableWidget, QTableWidgetItem
        from PyQt5.QtCore import Qt
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("表格显示测试")
        main_window.setGeometry(200, 200, 800, 400)
        
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建测试数据
        test_data = [
            {
                '算法类型': '经典机器学习',
                '算法名称': 'SVM分类器',
                '诊断结果': '正常',
                '置信度': '85.00%',
                '状态评估': '正常',
                '诊断时间': '2025-08-05 17:25:40'
            },
            {
                '算法类型': '深度学习',
                '算法名称': 'CNN模型',
                '诊断结果': '正常',
                '置信度': '92.00%',
                '状态评估': '正常',
                '诊断时间': '2025-08-05 17:25:40'
            }
        ]
        
        # 测试1: 基本表格（无样式）
        print("测试1: 基本表格（无样式）")
        table1 = QTableWidget()
        headers = list(test_data[0].keys())
        table1.setColumnCount(len(headers))
        table1.setHorizontalHeaderLabels(headers)
        table1.setRowCount(len(test_data))
        
        for i, item in enumerate(test_data):
            for j, header in enumerate(headers):
                value = item.get(header, '')
                table1.setItem(i, j, QTableWidgetItem(str(value)))
        
        table1.setFixedHeight(150)
        layout.addWidget(table1)
        
        # 测试2: 当前样式的表格
        print("测试2: 当前样式的表格")
        table2 = QTableWidget()
        table2.setColumnCount(len(headers))
        table2.setHorizontalHeaderLabels(headers)
        table2.setRowCount(len(test_data))
        
        for i, item in enumerate(test_data):
            for j, header in enumerate(headers):
                value = item.get(header, '')
                table2.setItem(i, j, QTableWidgetItem(str(value)))
        
        # 应用当前的样式
        ACCENT_COLOR = "#0066cc"
        table2.setStyleSheet(f"""
            QTableWidget {{
                gridline-color: #ddd;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border-bottom: 1px solid #eee;
                font-size: 14px;
            }}
            QTableWidget::item:selected {{
                background-color: #e3f2fd;
            }}
        """)
        
        table2.setFixedHeight(150)
        table2.setAlternatingRowColors(True)
        layout.addWidget(table2)
        
        # 测试3: 修复样式的表格
        print("测试3: 修复样式的表格")
        table3 = QTableWidget()
        table3.setColumnCount(len(headers))
        table3.setHorizontalHeaderLabels(headers)
        table3.setRowCount(len(test_data))
        
        for i, item in enumerate(test_data):
            for j, header in enumerate(headers):
                value = item.get(header, '')
                table3.setItem(i, j, QTableWidgetItem(str(value)))
        
        # 修复的样式 - 添加文字颜色
        table3.setStyleSheet(f"""
            QTableWidget {{
                gridline-color: #ddd;
                font-size: 14px;
                font-family: 'Microsoft YaHei';
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                color: black;
            }}
            QHeaderView::section {{
                background-color: {ACCENT_COLOR};
                color: white;
                font-weight: bold;
                padding: 10px;
                border: none;
                font-size: 14px;
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border-bottom: 1px solid #eee;
                font-size: 14px;
                color: black;
                background-color: white;
            }}
            QTableWidget::item:selected {{
                background-color: #e3f2fd;
                color: black;
            }}
            QTableWidget::item:alternate {{
                background-color: #f9f9f9;
                color: black;
            }}
        """)
        
        table3.setFixedHeight(150)
        table3.setAlternatingRowColors(True)
        layout.addWidget(table3)
        
        # 显示窗口
        main_window.show()
        
        print("✓ 测试窗口显示成功")
        print("请观察三个表格的显示效果:")
        print("  表格1: 基本表格（无样式）")
        print("  表格2: 当前样式")
        print("  表格3: 修复样式")
        print("窗口将在15秒后关闭...")
        
        # 15秒后关闭
        from PyQt5.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(15000)
        
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"✗ 表格显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_diagnosis_content_method():
    """测试诊断内容方法"""
    print("\n🔍 测试诊断内容方法...")
    
    try:
        # 设置Qt环境变量
        os.environ['QT_QPA_PLATFORM'] = 'windows'
        os.environ['QT_LOGGING_RULES'] = '*=false'
        
        from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from ui.report_generator import ReportGenerator
        from database.db_manager import DatabaseManager
        
        # 创建应用程序
        if not QApplication.instance():
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("诊断内容方法测试")
        main_window.setGeometry(300, 300, 900, 500)
        
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建报告生成器
        db = DatabaseManager()
        generator = ReportGenerator(db)
        
        # 创建测试数据
        test_data = [
            {
                '算法类型': '经典机器学习',
                '算法名称': 'SVM分类器',
                '诊断结果': '正常',
                '置信度': '85.00%',
                '状态评估': '正常',
                '诊断时间': '2025-08-05 17:25:40'
            },
            {
                '算法类型': '深度学习',
                '算法名称': 'CNN模型',
                '诊断结果': '正常',
                '置信度': '92.00%',
                '状态评估': '正常',
                '诊断时间': '2025-08-05 17:25:40'
            }
        ]
        
        # 使用实际的诊断内容方法
        diagnosis_widget = generator.create_diagnosis_content(test_data)
        layout.addWidget(diagnosis_widget)
        
        # 显示窗口
        main_window.show()
        
        print("✓ 诊断内容方法测试窗口显示成功")
        print("请观察诊断内容的显示效果")
        print("窗口将在15秒后关闭...")
        
        # 15秒后关闭
        from PyQt5.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(15000)
        
        app.exec_()
        
        return True
        
    except Exception as e:
        print(f"✗ 诊断内容方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始调试表格显示问题\n")
    
    # 测试简单表格
    simple_test = test_simple_table()
    
    # 测试诊断内容方法
    method_test = test_diagnosis_content_method()
    
    print(f"\n📊 测试结果:")
    print(f"   简单表格测试: {'✓ 通过' if simple_test else '✗ 失败'}")
    print(f"   诊断内容方法测试: {'✓ 通过' if method_test else '✗ 失败'}")


if __name__ == "__main__":
    main()
