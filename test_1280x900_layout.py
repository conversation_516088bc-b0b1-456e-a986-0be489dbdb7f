#!/usr/bin/env python3
"""
测试1280x900分辨率下的仪表盘界面显示效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer
from ui.dashboard import Dashboard
from database.db_manager import DatabaseManager

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """初始化测试窗口"""
        # 设置窗口尺寸为1280x900
        self.setGeometry(100, 100, 1280, 900)
        self.setFixedSize(1280, 900)
        self.setWindowTitle("仪表盘界面测试 - 1280x900分辨率")
        
        # 创建数据库管理器
        db_manager = DatabaseManager()
        
        # 创建仪表盘作为中央部件
        dashboard = Dashboard(db_manager)
        self.setCentralWidget(dashboard)
        
        print("测试窗口已创建")
        print(f"窗口尺寸: {self.width()}x{self.height()}")
        print("请检查界面显示效果：")
        print("1. 所有文字是否完整显示")
        print("2. UI元素是否有重叠")
        print("3. 布局是否协调美观")
        print("4. 字体大小是否合适")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = TestWindow()
    test_window.show()
    
    # 设置定时器自动关闭（可选）
    # timer = QTimer()
    # timer.timeout.connect(app.quit)
    # timer.start(15000)  # 15秒后自动关闭
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
